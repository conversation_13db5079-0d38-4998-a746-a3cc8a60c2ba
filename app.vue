<template>
  <div>
    <div v-if="loading">
      <SkeletonLoader class="absolute inset-0 z-40 mt-24 mx-12 md:mx-24" />
      <div class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
        <div class="text-center text-black">
          <i class="fa fa-spin fa-spinner fa-5x"></i>
        </div>
      </div>
    </div>
    <NuxtLayout v-else>
      <NuxtErrorBoundary>
        <NuxtPage />
        <template #error="{ error }">
          <div class="container mx-auto p-4">
            <button
              class="btn btn-primary"
              @click="error.value = null"
            >
              Try again
            </button>
          </div>
        </template>
      </NuxtErrorBoundary>
    </NuxtLayout>
  </div>
</template>

<script setup lang="ts">
const nuxtApp = useNuxtApp();
const loading = ref(true);

nuxtApp.hook("page:start", () => {
  loading.value = true;
});

nuxtApp.hook("page:finish", () => {
  loading.value = false;
});

// Handle errors globally
onErrorCaptured((err, instance, info) => {
  console.error('Error captured:', err);
  return false; // Prevent error from propagating
});
</script>
