// functions/api/keepz.js
// Keepz API integration using node-forge for proper PKCS1 padding support
// This implementation works in both Node.js and Cloudflare Workers

import forge from 'node-forge';

export async function onRequestGet(context) {
  const { request, env } = context;

  // CORS headers for better browser compatibility
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Content-Type': 'application/json'
  };

  try {
    // Environment variables (set in Cloudflare Pages dashboard)
    const KEEPZ_INTEGRATOR_ID = env.KEEPZ_INTEGRATOR_ID || "05382470-0ddb-4e36-8f23-4939f3b81ec9";
    const KEEPZ_IDENTIFIER = env.KEEPZ_IDENTIFIER || env.KEEPZ_INTEGRATOR_ID || "05382470-0ddb-4e36-8f23-4939f3b81ec9";
    const KEEPZ_RECEIVER_ID = env.KEEPZ_RECEIVER_ID || "0349ce8d-8061-4867-94a9-b9de0fb3fec6";
    const ORDER_AMOUNT = env.ORDER_AMOUNT || "25.50";
    const KEEPZ_BASE_URL = env.KEEPZ_BASE_URL || "https://gateway.dev.keepz.me/ecommerce-service";

    // RSA Keys from environment variables
    const KEEPZ_PUBLIC_KEY = env.KEEPZ_PUBLIC_KEY;
    const KEEPZ_PRIVATE_KEY = env.KEEPZ_PRIVATE_KEY;

    // Validate required environment variables
    if (!KEEPZ_PUBLIC_KEY) {
      console.error('Missing KEEPZ_PUBLIC_KEY environment variable');
      return new Response(JSON.stringify({
        error: 'Configuration error: KEEPZ_PUBLIC_KEY environment variable is required',
        statusCode: 400
      }), {
        status: 400,
        headers: corsHeaders
      });
    }

    if (!KEEPZ_PRIVATE_KEY) {
      console.error('Missing KEEPZ_PRIVATE_KEY environment variable');
      return new Response(JSON.stringify({
        error: 'Configuration error: KEEPZ_PRIVATE_KEY environment variable is required',
        statusCode: 500
      }), {
        status: 500,
        headers: corsHeaders
      });
    }

    // Parse URL query parameters to get order details
    const url = new URL(request.url);
    const amount = url.searchParams.get('amount');
    const orderId = url.searchParams.get('order_id');

    let orderDetails = {};
    if (amount) {
      orderDetails.amount = amount;
    }
    if (orderId) {
      orderDetails.integratorOrderId = orderId;
    }

    // Generate UUID using forge
    function generateUUID() {
      // Generate a random UUID v4
      const bytes = forge.random.getBytesSync(16);
      const hex = forge.util.bytesToHex(bytes);
      return [
        hex.substring(0, 8),
        hex.substring(8, 12),
        '4' + hex.substring(13, 16),
        ((parseInt(hex.substring(16, 17), 16) & 0x3) | 0x8).toString(16) + hex.substring(17, 20),
        hex.substring(20, 32)
      ].join('-');
    }

    // Convert a custom order ID to UUID format
    function convertToUUID(customId) {
      // Check if it's already a valid UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (uuidRegex.test(customId)) {
        return customId;
      }

      // Create a deterministic UUID based on the custom ID
      // Use MD5 hash of the custom ID to create consistent UUID
      const md = forge.md.md5.create();
      md.update(customId);
      const hash = md.digest().toHex();

      // Format as UUID v4
      return [
        hash.substring(0, 8),
        hash.substring(8, 12),
        '4' + hash.substring(13, 16),
        ((parseInt(hash.substring(16, 17), 16) & 0x3) | 0x8).toString(16) + hash.substring(17, 20),
        hash.substring(20, 32)
      ].join('-');
    }

    // Convert base64 key to PEM format
    function convertToPemFormat(base64Key, keyType = 'PUBLIC') {
      const pemHeader = keyType === 'PUBLIC' ? '-----BEGIN PUBLIC KEY-----' : '-----BEGIN PRIVATE KEY-----';
      const pemFooter = keyType === 'PUBLIC' ? '-----END PUBLIC KEY-----' : '-----END PRIVATE KEY-----';
      const pemBody = base64Key.match(/.{1,64}/g).join('\n');
      return `${pemHeader}\n${pemBody}\n${pemFooter}`;
    }

    // Encrypt data using RSA public key with PKCS1 padding (node-forge)
    function encryptData(data, publicKeyPem) {
      try {
        // Parse the PEM public key
        const publicKey = forge.pki.publicKeyFromPem(publicKeyPem);

        // Encrypt with PKCS1 padding (what Keepz API expects)
        const encrypted = publicKey.encrypt(data, 'RSAES-PKCS1-V1_5');

        // Convert to base64
        return forge.util.encode64(encrypted);
      } catch (error) {
        console.error('Encryption failed:', error.message);
        throw error;
      }
    }

    // Decrypt data using RSA private key with PKCS1 padding (node-forge)
    function decryptData(encryptedData, privateKeyPem) {
      try {
        // Parse the PEM private key
        const privateKey = forge.pki.privateKeyFromPem(privateKeyPem);

        // Decode from base64
        const encrypted = forge.util.decode64(encryptedData);

        // Try PKCS1 padding first (what Keepz API uses)
        try {
          const decrypted = privateKey.decrypt(encrypted, 'RSAES-PKCS1-V1_5');
          return decrypted;
        } catch (pkcs1Error) {
          console.log('PKCS1 decryption failed, trying OAEP...');
          // Fallback to OAEP padding
          const decrypted = privateKey.decrypt(encrypted, 'RSA-OAEP');
          return decrypted;
        }
      } catch (error) {
        console.error('Decryption failed:', error.message);
        throw error;
      }
    }

    // Create the order data object
    const orderData = {
      amount: parseFloat(orderDetails.amount || ORDER_AMOUNT),
      receiverId: orderDetails.receiverId || KEEPZ_RECEIVER_ID,
      receiverType: orderDetails.receiverType || "BRANCH",
      integratorId: orderDetails.integratorId || KEEPZ_INTEGRATOR_ID,
      integratorOrderId: orderDetails.integratorOrderId ? convertToUUID(orderDetails.integratorOrderId) : generateUUID()
    };

    // console.log('Order data to be encrypted:', JSON.stringify(orderData, null, 2));

    // Convert JSON to string and encrypt it
    const jsonData = JSON.stringify(orderData);
    const publicKeyPem = convertToPemFormat(KEEPZ_PUBLIC_KEY, 'PUBLIC');

    // console.log('Using public key for encryption with PKCS1 padding');

    let encryptedData;
    try {
      encryptedData = encryptData(jsonData, publicKeyPem);
      // console.log('Successfully encrypted data');
      // console.log('Encrypted data length:', encryptedData.length);
    } catch (error) {
      // console.error('Failed to encrypt data:', error.message);
      return new Response(JSON.stringify({
        error: `Failed to encrypt data: ${error.message}`,
        statusCode: 500
      }), {
        status: 500,
        headers: corsHeaders
      });
    }

    // Prepare the API request payload
    const requestPayload = {
      encryptedData: encryptedData,
      identifier: KEEPZ_IDENTIFIER
    };

    // console.log('Request payload prepared');
    // console.log('Identifier:', KEEPZ_IDENTIFIER);

    // Make the API request
    const apiUrl = `${KEEPZ_BASE_URL}/api/integrator/order`;
    // console.log('Making API call to:', apiUrl);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestPayload)
    });

    const responseData = await response.text();

    // console.log('Response Status:', response.status);
    // console.log('Response Headers:', JSON.stringify([...response.headers.entries()]));
    // console.log('Response Body:', responseData);

    if (response.status === 200 || response.status === 201) {
      try {
        const responseObj = JSON.parse(responseData);
        // console.log('\nAPI call successful!');

        if (responseObj.encryptedData) {
          // console.log('Encrypted response data received');

          // Decrypt the response using private key
          const privateKeyPem = convertToPemFormat(KEEPZ_PRIVATE_KEY, 'PRIVATE');

          try {
            const decryptedResponse = decryptData(responseObj.encryptedData, privateKeyPem);
            // console.log('Successfully decrypted response');
            // console.log('Decrypted response:', JSON.stringify(JSON.parse(decryptedResponse), null, 2));

            const decryptedObj = JSON.parse(decryptedResponse);

            const result = {
              success: true,
              systemId: decryptedObj.systemId,
              integratorOrderId: decryptedObj.integratorOrderId,
              urlForQR: decryptedObj.urlForQR
            };

            // if (decryptedObj.systemId) {
            //   console.log('\nOrder created successfully!');
            //   console.log('System ID:', decryptedObj.systemId);
            //   console.log('Integrator Order ID:', decryptedObj.integratorOrderId);
            //   if (decryptedObj.urlForQR) {
            //     console.log('QR URL:', decryptedObj.urlForQR);
            //   }
            // }

            return new Response(JSON.stringify(result, null, 2), {
              status: 200,
              headers: corsHeaders
            });
          } catch (decryptError) {
            console.error('Failed to decrypt response:', decryptError.message);
            return new Response(JSON.stringify({
              error: `Failed to decrypt response: ${decryptError.message}`,
              statusCode: 500
            }), {
              status: 500,
              headers: corsHeaders
            });
          }
        } else {
          console.log('No encrypted data in response');
          return new Response(JSON.stringify({
            success: true,
            message: 'No encrypted data in response',
            response: responseObj
          }, null, 2), {
            status: 200,
            headers: corsHeaders
          });
        }
      } catch (parseError) {
        console.log('Response is not valid JSON:', parseError.message);
        return new Response(JSON.stringify({
          error: `Response is not valid JSON: ${parseError.message}`,
          statusCode: 500
        }), {
          status: 500,
          headers: corsHeaders
        });
      }
    } else {
      console.error('API call failed with status:', response.status);
      return new Response(JSON.stringify({
        error: `API call failed with status: ${response.status}`,
        responseBody: responseData,
        statusCode: response.status
      }), {
        status: response.status,
        headers: corsHeaders
      });
    }

  } catch (error) {
    console.error('Function error:', error.message);
    return new Response(JSON.stringify({
      error: `Function error: ${error.message}`,
      statusCode: 500
    }), {
      status: 500,
      headers: corsHeaders
    });
  }
}

// Handle OPTIONS requests for CORS
export async function onRequestOptions(context) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    }
  });
}

// Handle other HTTP methods
export async function onRequest(context) {
  const { request } = context;

  if (request.method === 'OPTIONS') {
    return onRequestOptions(context);
  }

  if (request.method !== 'GET') {
    return new Response(JSON.stringify({
      error: 'Method not allowed. Only GET requests are supported.',
      statusCode: 405
    }), {
      status: 405,
      headers: {
        'Allow': 'GET, OPTIONS',
        'Content-Type': 'application/json'
      }
    });
  }

  // This shouldn't be reached due to onRequestGet, but just in case
  return new Response(JSON.stringify({
    error: 'Use GET method',
    statusCode: 405
  }), {
    status: 405,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}
