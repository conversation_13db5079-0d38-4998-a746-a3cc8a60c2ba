import Toast from "vue-toastification";
import "vue-toastification/dist/index.css";
import { useToast } from "vue-toastification";

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.use(Toast, {
    hideProgressBar: true,
  });
  const toast = useToast();

  return {
    provide: {
      infoToast: (text: string) => {
        toast.info(text, {
          timeout: 2000,
          position: "top-left",
        });
      },
      warningToast: (text: string) => {
        toast.warning(text, {
          timeout: 2000,
          position: "top-left",
        });
      },
      successToast: (text: string) => {
        toast.success(text, {
          timeout: 2000,
          position: "top-left",
        });
      },
    },
  };
});
