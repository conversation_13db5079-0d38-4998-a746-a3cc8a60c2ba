import PusherSingleton from '~/utils/pusherSingleton';

export default defineNuxtPlugin((nuxtApp) => {
  const config = useRuntimeConfig();
  
  const pusherClient = PusherSingleton.getInstance({
    key: config.public.pusherKey,
    cluster: config.public.pusherCluster,
    wsHost: config.public.pusherHost,
    wsPort: 443,
    wssPort: 443,
    forceTLS: true,
    disableStats: true,
    enabledTransports: ["ws", "wss"]
  });

  return {
    provide: {
      pusher: pusherClient,
      pusherSingleton: PusherSingleton
    }
  };
});
