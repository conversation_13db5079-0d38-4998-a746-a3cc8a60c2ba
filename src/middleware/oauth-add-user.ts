import {
  OAUTH_REFRESH_TOKEN_COOKIE,
  OAUTH_TOKEN_COOKIE,
} from "../types/authTypes";

export default defineNuxtRouteMiddleware(async (to, from) => {
  const user = useAuthUser();
  const supUser = useSupabaseUser();
  const { registerWithOAuth, fetchAndLoginOauthUser } = useAuthMethods();
  const { id } = useId();

  const oauth_token = useCookie(OAUTH_TOKEN_COOKIE);
  const refresh_token = useCookie(OAUTH_REFRESH_TOKEN_COOKIE);

  if (!supUser.value || !supUser.value?.email) {
    return navigateTo("/account");
  }

  if (user.value && supUser.value) {
    return navigateTo("/account");
  }

  if (!user.value && supUser.value) {
    try {
      const fetchResponse = await fetchAndLoginOauthUser(
        supUser.value?.email as string
      );
      if (fetchResponse.status) {
        if (id.value) {
          return navigateTo("/t/" + id.value);
        } else {
          return navigateTo("/account");
        }
      } else {
        const registerResponse = await registerWithOAuth(
          supUser.value?.user_metadata.name
            ? (supUser.value?.user_metadata.name.split(" ")[0] as string)
            : "",
          supUser.value?.user_metadata.name &&
            supUser.value?.user_metadata.name.split(" ").length > 1
            ? (supUser.value?.user_metadata.name.split(" ")[1] as string)
            : "",
          supUser.value?.email as string,
          supUser.value?.phone as string,
          supUser.value?.app_metadata.provider as string,
          oauth_token.value ? (oauth_token.value as string) : "",
          refresh_token.value ? (refresh_token.value as string) : ""
        );
        if (registerResponse.status && user.value) {
          if (id.value) {
            return navigateTo("/t/" + id.value);
          } else {
            return navigateTo("/account");
          }
        }
      }
    } catch (error) {
      return navigateTo("/account");
    }
  }
});
