import {
  OAUTH_REFRESH_TOKEN_COOKIE,
  OAUTH_TOKEN_COOKIE,
  User,
} from "../types/authTypes";

export default defineNuxtRouteMiddleware(async (to, from) => {
  const user = useAuthUser();
  const supUser = useSupabaseUser();
  const { auth } = useSupabaseClient();

  const { id } = useId();
  const loading = useLoading();
  const { registerWithOAuth, fetchAndLoginOauthUser } = useAuthMethods();

  const oauth_token = useCookie(OAUTH_TOKEN_COOKIE);
  const refresh_token = useCookie(OAUTH_REFRESH_TOKEN_COOKIE);

  if (user.value) {
    if (id.value) {
      return navigateTo("/t/" + id.value);
    } else {
      return navigateTo("/account");
    }
  } else if (supUser.value && !user.value) {
    try {
      const fetchResponse = await fetchAndLoginOauthUser(
        supUser.value?.email as string
      );
      console.log(fetchResponse);
      if (fetchResponse.status) {
        user.value = fetchResponse.data as User;
        console.log(user.value);
        if (user.value) {
          console.log("We accounting");
          return navigateTo("/account");
        } else {
          throw new Error("Login Failed");
        }
      } else {
        const registerResponse = await registerWithOAuth(
          supUser.value?.user_metadata.name
            ? (supUser.value?.user_metadata.name.split(" ")[0] as string)
            : "",
          supUser.value?.user_metadata.name &&
            supUser.value?.user_metadata.name.split(" ").length > 1
            ? (supUser.value?.user_metadata.name.split(" ")[1] as string)
            : "",
          supUser.value?.email as string,
          supUser.value?.phone as string,
          supUser.value?.app_metadata.provider as string,
          oauth_token.value ? (oauth_token.value as string) : "",
          refresh_token.value ? (refresh_token.value as string) : ""
        );
        if (registerResponse.status && user.value) {
          return navigateTo("/account");
        } else {
          throw new Error("Register Failed");
        }
      }
    } catch (error) {
      console.error("Error in login middleware", error);
      await auth.signOut();
      return;
    }
  } else {
    return;
  }
});
