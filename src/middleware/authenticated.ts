export default defineNuxtRouteMiddleware(async (to, from) => {
  const user = useAuthUser();
  const { id } = useId();
  const { auth } = useSupabaseClient();
  const supUser = useSupabaseUser();

  if (
    user.value &&
    user.value.oauth_token &&
    user.value.refresh_token &&
    user.value.provider_name &&
    !supUser.value
  ) {
    const { data, error } = await auth.setSession({
      access_token: user.value.oauth_token,
      refresh_token: user.value.refresh_token,
    });
    if (error) {
      console.log(error);
    }
  }

  // Check if the user is authenticated
  if (user.value && user.value?.token) {
    return;
  } else {
    if (id.value) {
      return navigateTo("/t/" + id.value);
    } else {
      return navigateTo("/login");
    }
  }
});
