export default defineNuxtRouteMiddleware(async (to, from) => {
  // Skip middleware for error page
  if (to.path === '/error') return;

  if (to.path.startsWith("/t")) {
    const user = useAuthUser();
    const supUser = useSupabaseUser();
    const { $warningToast } = useNuxtApp();
    const loading = useLoading();

    // Check for id value and reroute if there is a user accordingly
    if (to.params.id) {
      return;
    } else {
      $warningToast("Source page not found, redirecting...");
      if (user.value) {
        loading.value = false;
        return navigateTo("/account");
      } else if (supUser.value && !user.value) {
        loading.value = false;
        return navigateTo("/confirm");
      } else {
        loading.value = false;
        return navigateTo("/login");
      }
    }
  }
  // Allow all other routes to pass through
  return;
});
