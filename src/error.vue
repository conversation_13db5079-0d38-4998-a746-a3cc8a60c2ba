<script setup>
const error = useError();
const router = useRouter();

const handleError = () => {
  clearError({ redirect: "/" });
  router.push('/');
}
</script>

<template>
  <NuxtLayout>
    <div class="container custom-padding">
      <div class="row justify-content-center">
        <NuxtImg class="custom-width" src="/img/404.png" />
      </div>
      <div class="row justify-content-center">
        <h2 class="text-center">{{ error?.message || 'Page not found' }}</h2>
      </div>
      <div class="row justify-content-center mt-4">
        <button @click="handleError" class="btn btn-lg btn-primary">
          GO HOME
        </button>
      </div>
    </div>
  </NuxtLayout>
</template>

<style scoped>
.custom-width {
  width: 100%;
  max-width: 450px;
}

.custom-padding {
  padding-block: 8rem;
}
</style>
