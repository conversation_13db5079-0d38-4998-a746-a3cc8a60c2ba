import { Order, OrderData } from "~~/src/types/orderTypes";
import { Discount, Dish } from "~~/src/types/commonTypes";
export function useOrder() {
  const { $warningToast } = useNuxtApp();
  const { encodedID } = useId();
  const { cartDishes } = useCart();
  const orders = useState<Order[] | null>("order", () => null);
  const pending = ref(false);
  const { t } = useI18n();
  const user = useAuthUser();

  const discount = useState<Discount | null>("discount", () => null);
  const discountedTotal = useState("discountedTotal", () => 0);

  const getOrders = async () => {
    if (!encodedID.value) {
      return;
    }
    const { data: orderData } = await apiGet<OrderData>(
      `order/${encodedID.value}`
    );

    const orders = orderData.value?.data;
    if (orders) {
      setOrders(orders);
    }
  };

  const setOrders = (ordersData: Order[]) => {
    const filteredOrders = ordersData.filter((order) => order.status == 1);
    orders.value = filteredOrders;
  };

  const putOrder = async (dishIdsString: string) => {
    const response = await apiPost("/order", {
      table_id: encodedID.value,
      dishes: dishIdsString,
      client_id: user.value && user.value.token ? user.value.id : null,
    });
    if (response.error.value) {
      pending.value = false;
      return response;
    } else {
      return response;
    }
  };

  const orderedDishesPrice = computed((): number => {
    let result: number = 0;
    if (orders.value) {
      for (let order of orders.value) {
        order.dish?.forEach((dish) => {
          // Check if there's a promo and if newPrice is set in the first promo
          const effectivePrice =
            dish.promos &&
            dish.promos.length > 0 &&
            dish.promos[0] &&
            "newPrice" in dish.promos[0]
              ? Number(dish.promos[0].newPrice)
              : Number(dish?.price || "0");

          result += effectivePrice * (dish?.quantity || 1);
        });
      }
    }
    return result;
  });

  const addOrder = async () => {
    pending.value = true;
    let cartArray = cartDishes?.value || [];
    const result = cartArray.reduce((acc, dish) => {
      return acc + `${dish.id}-${dish.quantity},`;
    }, "");
    const { data, error } = await putOrder(result);

    if (error.value) {
      $warningToast(t("awaitingCheckout"));
    }
    pending.value = false;

    return data;
  };

  const numberOfDishesInOrder = computed((): number => {
    return orders.value?.length || 0;
  });

  return {
    putOrder,
    addOrder,
    pending,
    getOrders,
    orders,
    orderedDishesPrice,
    numberOfDishesInOrder,
    discount,
    discountedTotal,
  };
}
