import { computed } from 'vue';
import type { Ref } from 'vue';
import { useCookie, useNuxtApp } from "#app";
import { useI18n } from 'vue-i18n';

export const useLocale = () => {
  const { $i18n } = useNuxtApp();
  const localeCookie = useCookie('locale');
  
  // Initialize cookie if not set
  if (!localeCookie.value) {
    localeCookie.value = $i18n.locale.value;
  }

  const setLocale = (newLocale: string) => {
    $i18n.locale.value = newLocale;
    localeCookie.value = newLocale;
  };

  return {
    locale: computed(() => $i18n.locale.value),
    setLocale
  };
};
