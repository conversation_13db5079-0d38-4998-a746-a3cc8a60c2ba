export function useModal() {
  const modals = useState<{ id: string; visible: boolean }[]>(
    "modals",
    () => []
  );

  const registerModal = (id: string): void => {
    const current = modals.value.find((modal) => modal.id === id);

    if (!current)
      modals.value.push({
        id: id,
        visible: false,
      });
  };

  const isModalOpen = (id: string) => {
    return computed((): boolean => {
      for (let modal of modals.value) {
        if (modal.id === id) {
          return modal.visible;
        }
      }
      return false;
    });
  };

  const openModal = (id: string): void => {
    const modal = modals.value.find((modal) => modal.id === id);

    if (modal) {
      modal.visible = true;
    }
  };

  const closeModal = (id: string): void => {
    const modal = modals.value.find((modal) => modal.id === id);

    if (modal) {
      modal.visible = false;
    }
  };

  return {
    registerModal,
    isModalOpen,
    openModal,
    closeModal,
    modals,
  };
}
