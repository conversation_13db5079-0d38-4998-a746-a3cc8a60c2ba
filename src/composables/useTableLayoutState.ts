import { provide, inject, ref } from 'vue'

const TABLE_LAYOUT_INITIALIZED_KEY = 'tableLayoutInitialized'

export const useTableLayoutState = () => {
  const isInitialized = useState('tableLayoutInitialized', () => false)

  const provideTableLayoutState = () => {
    provide(TABLE_LAYOUT_INITIALIZED_KEY, isInitialized)
  }

  const injectTableLayoutState = () => {
    return inject(TABLE_LAYOUT_INITIALIZED_KEY, ref(false))
  }

  return {
    isInitialized,
    provideTableLayoutState,
    injectTableLayoutState
  }
}