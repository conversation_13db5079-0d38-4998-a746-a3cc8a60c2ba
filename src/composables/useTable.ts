import { TableData, Table, Item } from "../types/table-types";
import { Category, CategoryList, GalleryItem, Restaurant } from "../types/commonTypes";

export function useTable() {
  const { id } = useId();
  const { locale } = useLocale();
  
  const table = useState<Table | null>("table", () => null);

  const getTable = async (tableId: string) => {
    if (!id.value || (table.value && Object.keys(table.value).length > 0)) {
      return;
    }
    const { cashed: tableData, error } = await apiGetCashe<TableData>(
      `t/${tableId || id.value}`
    );

    // if (!tableData.value) {
    //   throw createError({
    //     statusCode: 404,
    //     statusMessage: "Table not found",
    //     fatal: true,
    //   });
    // }
    if (tableData.value?.data) {
      setTable(tableData.value.data);
    }
  };

  const setTable = (tableData: Table) => {
    table.value = tableData;
  };

  const items = computed((): Item[] | null => {
    if (table.value?.items) {
      return table.value.items;
    } else {
      return null;
    }
  });

  const gallery = computed((): GalleryItem[] | null => {
    if (table.value?.gallery) {
      return [...table.value.gallery].sort((a, b) => a.placement - b.placement);
    } else {
      return null;
    }
  });

  const searchedItems = (searchTerm: string): Item[] => {
    let query = searchTerm.toLowerCase();

    if (query === "") {
      return items.value || [];
    }

    if (query[0] === "#") {
      query = query.substring(1);
      return (
        items.value?.filter((item) =>
          item.category?.some((cat) =>
            cat.translations?.some((tr) =>
              tr.title?.toLocaleLowerCase().includes(query)
            )
          )
        ) || []
      );
    } else {
      return (
        items.value?.filter((item) => {
          return item?.translations?.some((tr) =>
            tr?.title?.toLowerCase()?.includes(query)
          );
        }) || []
      );
    }
  };

  const restaurant = computed((): Restaurant | null => {
    if (table.value?.restaurant) {
      return table.value?.restaurant;
    } else {
      return null;
    }
  });

  const sortedCategories = computed(() => {
    const categories: { [key: string]: number } = {};
    const catData: { [key: string]: Category } = {};
    const categoriesList: CategoryList[] = [];

    const currentLocale = locale.value;

    if (items.value) {
      for (let item of items.value) {
        item.category?.forEach((category) => {
          category.translations?.forEach((tr) => {
            if (currentLocale === tr?.locale && tr?.locale) {
              if (typeof tr.title === "string" && tr.title !== "") {
                if (categories[tr.title]) {
                  categories[tr.title]++;
                } else {
                  categories[tr.title] = 1;
                  catData[tr.title] = category;
                }
              }
            }
          });
          if (
            category &&
            !category.translations?.some((obj) => obj.locale === currentLocale)
          ) {
            if (category.title) {
              if (categories[category.title]) {
                categories[category.title]++;
              } else {
                categories[category.title] = 1;
                catData[category.title] = category;
              }
            }
          }
        });
      }
    }

    if (Object.keys(categories).length > 0) {
      for (let category in categories) {
        categoriesList.push({
          name: category,
          count: categories[category],
          data: catData[category],
        });
      }
    }

    // Sort first by order_by if it exists, then by count
    return categoriesList.sort((a, b) => {
      // If both categories have order_by, sort by it
      if (a.data?.order_by !== undefined && b.data?.order_by !== undefined) {
        return (a.data.order_by || 0) - (b.data.order_by || 0);
      }
      // If only one has order_by, prioritize it
      if (a.data?.order_by !== undefined) return -1;
      if (b.data?.order_by !== undefined) return 1;
      // If neither has order_by, sort by count
      return b.count - a.count;
    });
  });

  return {
    getTable,
    table,
    items,
    restaurant,
    sortedCategories,
    searchedItems,
    gallery,
  };
}
