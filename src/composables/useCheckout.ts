import { Checkout } from "types/commonTypes";

export function useCheckout() {
  const { $warningToast } = useNuxtApp();
  const { orders } = useOrder();
  const router = useRouter();
  const { t } = useI18n();
  const config = useRuntimeConfig();

  const pending = ref(false);
  const { encodedID } = useId();
  const user = useAuthUser();

  const indicator = ref("");

  const checkout = async (
    method: string,
    tips = 0,
    discount_code = null,
    change = null
  ) => {
    indicator.value = method;
    pending.value = true;
    const { data, error } = await apiPost<{
      data: Partial<Checkout>;
    }>("/checkout", {
      table_id: encodedID.value,
      payment_method: method,
      tips,
      discount_code,
      change,
    });
    const client_ids = orders.value
      ?.filter(
        (order) => order.client_id !== undefined && order.client_id !== null
      )
      .map((order) => order.client_id)
      .join(",");

    if (error.value) {
      $warningToast(t("NoOrdPayMethod"));
    } else {
      // Get and map the client_ids to comma separated string
      orders.value = [];
    }

    pending.value = false;

    return { data, error, client_ids };
  };

  const getCheck = async (checkId: string) => {
    pending.value = true;
    const { data: check, error } = await apiGet(`/checkout/${checkId}`);
    if (error.value) {
      $warningToast(t("NoOrdPayMethod"));
    }

    return { check, error };
  };

  return { getCheck, checkout, pending, indicator };
}
