import { CartData } from "../types/cartTypes";
import { Dish } from "../types/commonTypes";
import { useTable } from "./useTable"; // Import useTable

export function useCart() {
  const { $warningToast } = useNuxtApp();
  const { encodedID } = useId();
  const { items: allDishes } = useTable(); // Get all available dishes
  const cartDishes = useState<Dish[] | null>("cartDishes", () => null);
  const pending = ref(false);
  const { t } = useI18n();
  const skipNextPusherUpdate = ref(false);
  const isLocalUpdate = ref(false);  // New flag to track local vs remote updates

  const getCart = async () => {
    // Only skip if it's a local update
    if (!encodedID.value || (skipNextPusherUpdate.value && isLocalUpdate.value)) {
      skipNextPusherUpdate.value = false;
      isLocalUpdate.value = false;
      return;
    }
    const { data: cartData } = await apiGet<CartData>(
      `cart/${encodedID.value}`
    );
    const dishes = cartData.value?.data?.dish;
    if (dishes) {
      setCartDishes(dishes);
    }
  };

  const setCartDishes = (dishes: Dish[]) => {
    cartDishes.value = dishes;
  };

  const putDish = async (dishIdsString: string) => {
    isLocalUpdate.value = true;  // Mark this as a local update
    skipNextPusherUpdate.value = true;
    const response = await apiPost<CartData>("/cart", {
      table_id: encodedID.value,
      dishes: dishIdsString,
    });
    return response;
    // if (response?.error?.value) {
    //   $warningToast(t("canNotUpdateCart"));
    //   return response;
    // } else {
    //   return response;
    // }
  };

  const createDishIdsString = (itemModifier: any) => {
    return cartDishes?.value?.map(itemModifier).join(",") || "0-0";
  };

  const updateCartDishes = (itemModifier: any) => {
    if (cartDishes.value) {
      cartDishes.value = cartDishes?.value?.map(itemModifier);
    }
  };

  const addDish = async (id?: number) => {
    if (!id) {
      console.error("addDish called without an ID.");
      return;
    }
    // Remove pending state
    // pending.value = true;

    // Find the full dish details from the available items
    const dishToAddDetails = allDishes.value?.find(dish => dish.id === id);

    if (!dishToAddDetails) {
      console.error(`Dish with ID ${id} not found.`);
      $warningToast(t("canNotAddDishInCart")); // Inform user if dish details are missing
      return;
    }

    // Check if dish already exists in cart
    const existingDish = cartDishes.value?.find(dish => dish.id === id);
    if (existingDish) {
        // Optionally, you could call increaseQuantity(id) here or show a message
        console.warn(`Dish with ID ${id} already in cart. Use increaseQuantity.`);
        $warningToast('Dish already in cart.'); // Let user know
        return;
    }


    // Create the dish object for the cart, including all details
    const dishToAdd = { ...dishToAddDetails, quantity: 1 };


    // Immediately add the full dish object to the cart
    cartDishes.value = [...(cartDishes.value || []), dishToAdd];


    // --- Remove old logic that added partial data ---
    // const dishes = cartDishes?.value?.map((dish) => [
    //   `${dish?.id}-${dish?.quantity}`,
    // ]);
    // let dishIdsString = null;
    // if (dishes) {
    //   dishIdsString = dishes?.join(",") + `,${id}-1` || "0-0";
    // } else {
    //   dishIdsString = `,${id}-1` || "0-0";
    // }
    // const tempDish = { id, quantity: 1 };
    // cartDishes.value = [...(cartDishes.value || []), tempDish];
    // --- End of removed old logic ---

    // Remove API call
    // const res = await putDish(dishIdsString as string);

    // Remove API response handling logic
    // ... (rest of the commented-out API handling code) ...

  };

  const increaseQuantity = async (id?: number) => {
    // Remove pending state
    // pending.value = true;

    // Temporarily update the quantity in the cart
    updateCartDishes((dish: Dish) =>
      dish?.id === id ? { ...dish, quantity: (dish?.quantity || 0) + 1 } : dish
    );

    const dishIdsString = createDishIdsString((dish: Dish) =>
      dish?.id === id
        ? `${dish?.id}-${dish?.quantity || 0}`
        : `${dish?.id}-${dish?.quantity}`
    );
    // Remove API call
    // const res = await putDish(dishIdsString);

    // Remove setTimeout block related to API call
    // setTimeout(() => {
    //   if (res?.error?.value) {
    //     // Revert the quantity update if there's an error
    //     updateCartDishes((dish: Dish) =>
    //       dish?.id === id
    //         ? { ...dish, quantity: (dish?.quantity || 0) - 1 }
    //         : dish
    //     );
    //     $warningToast(t("canNotUpdateCart"));
    //   }
    //   pending.value = false;
    // }, 50);
  };

  const decreaseQuantity = async (id?: number) => {
    // Remove pending state
    // pending.value = true;

    const dishIdsString = createDishIdsString((dish: Dish) =>
      dish?.id === id && (dish?.quantity || 0) === 1
        ? ["0-0"]
        : dish?.id === id
        ? [`${dish?.id}-${(dish?.quantity || 0) - 1}`]
        : [`${dish?.id}-${dish?.quantity}`]
    );

    // Store the current state of the cart to revert back in case of an error
    const originalCartState = [...(cartDishes.value || [])];

    // Temporarily decrease the quantity in the cart
    updateCartDishes((dish: Dish) => {
      if (dish?.id === id) {
        return dish?.quantity === 1
          ? null
          : { ...dish, quantity: (dish?.quantity || 0) - 1 };
      }
      return dish;
    });

    // Remove dish if quantity becomes 0
    if (cartDishes.value) {
      cartDishes.value = cartDishes?.value?.filter(Boolean);
    }

    // Remove API call
    // const res = await putDish(dishIdsString);

    // Remove setTimeout block related to API call
    // setTimeout(() => {
    //   if (res?.error?.value) {
    //     // Revert to the original cart state if there's an error
    //     cartDishes.value = originalCartState;
    //     $warningToast(t("canNotUpdateCart"));
    //   }
    //   pending.value = false;
    // }, 50);
  };

  const clearCart = async () => {
    // Remove API call
    // const resu = await putDish("0-0");
    cartDishes.value = [];
  };

  const cartDishesPrice = computed((): number => {
    let result: number = 0;
    if (cartDishes.value) {
      for (let dish of cartDishes.value) {
        result += parseFloat(dish?.price || "0") * (dish?.quantity || 1);
      }
    }
    return result;
  });

  const numberOfDishesInCart = computed((): number => {
    return cartDishes.value?.length || 0;
  });

  return {
    putDish,
    clearCart,
    decreaseQuantity,
    increaseQuantity,
    pending,
    addDish,
    getCart,
    cartDishes,
    cartDishesPrice,
    numberOfDishesInCart,
  };
}
