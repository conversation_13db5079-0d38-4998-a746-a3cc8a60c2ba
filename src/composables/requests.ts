enum HttpMethod {
  GET = "GET",
  POST = "POST",
  PUT = "PUT",
  DELETE = "DELETE",
}

function options(method: any, body?: object) {
  const runtimeConfig = useRuntimeConfig();
  return {
    baseURL: runtimeConfig.public.apiBase,
    headers: {
      "Content-Type": "application/json",
    },
    method,
    body,
  };
}

export const apiRequest = async <T>(
  url: string,
  method: HttpMethod,
  body?: object
) => {
  const { data, error } = await useFetch<T>(url, options(method, body));

  return { data, error };
};

export const apiGet = async <T>(url: string) => {
  return apiRequest<T>(url, HttpMethod.GET);
};

export const apiPost = async <T>(url: string, data?: object) => {
  return apiRequest<T>(url, HttpMethod.POST, data);
};

export const apiPut = async <T>(url: string, data?: object) => {
  return apiRequest<T>(url, HttpMethod.PUT, data);
};

export const apiDelete = (url: string, data: object) => {
  return apiRequest(url, HttpMethod.DELETE, data);
};

export const apiGetCashe = async <T>(url: string) => {
  const { cashed, error } = await useFetchWithCashe<T>(
    url,
    options(HttpMethod.GET)
  );

  return { cashed, error };
};
