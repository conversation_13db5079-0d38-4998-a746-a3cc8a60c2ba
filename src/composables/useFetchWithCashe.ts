// import { StorageSerializers, useSessionStorage } from "@vueuse/core";
// import _ from "lodash";

// export default async <T>(url: string, options: any) => {
//   const error = ref<any>(null);
//   const pending = ref<any>(null);

//   const cashed = useSessionStorage<T>(url, null, {
//     serializer: StorageSerializers.object,
//   });

//   if (!cashed.value) {
//     const {
//       data,
//       error: fetchError,
//       pending: fetchPending,
//     } = await useFetch<T>(url, options);
//     cashed.value = data.value as T;
//     pending.value = fetchPending;
//     error.value = fetchError;
//   } else {
//     pending.value = false;
//     error.value = false;
//   }

//   return { cashed, error };
// };
import { ref } from "vue";
import _ from "lodash";

export default async <T>(url: string, options: any) => {
  const cashed = ref<T>();
  const error = ref<any>(null);
  const pending = ref<any>(true);

  const fetchData = async () => {
    const {
      data,
      error: fetchError,
      pending: fetchPending,
    } = await useFetch<T>(url, options);

    cashed.value = data.value as T;
    pending.value = fetchPending;
    error.value = fetchError;
  };

  await fetchData();

  return { cashed, error };
};
