import { Checkout } from "../types/commonTypes";
import { ref, onBeforeMount } from "vue";
import { useToast } from "vue-toastification";

export default function useHistory() {
  const checkouts = ref<Checkout[]>([]);
  const loading = ref(true);
  const user = useAuthUser().value;
  const config = useRuntimeConfig();

  const token = user?.token;
  const apiPath = "/cp/checkout/client/";
  const createCheckoutApiPath = "/cp/checkout";

  const toast = useToast();

  const fetchCheckouts = async () => {
    loading.value = true;
    try {
      if (user && user?.id) {
        const url = new URL(
          apiPath + encodeURIComponent(user.id),
          config.public.capiBase
        ).href;

        const response = await fetch(url, {
          headers: { Authorization: "Bearer " + token },
          method: "GET",
        });
        const data = await response.json();

        if (data.status) {
          checkouts.value = data.data;
        } else {
          throw new Error(`Server responded with status: ${data.message}`);
        }
      }
    } catch (error: any) {
      toast.error(
        error.message || "An error occurred while fetching checkouts",
        {
          timeout: 1000,
        }
      );
    } finally {
      loading.value = false;
    }
  };

  const postHistoryData = async (checkoutData: Partial<Checkout>) => {
    try {
      const url = new URL(createCheckoutApiPath, config.public.capiBase).href;

      const response = await fetch(url, {
        headers: { Authorization: "Bearer " + token },
        method: "POST",
        body: JSON.stringify(checkoutData),
      });
      const data = await response.json();

      if (data.status) {
        return data;
      } else {
        throw new Error(`Server responded with status: ${data.message}`);
      }
    } catch (error: any) {
      toast.error("An error occurred while creating checkout for history", {
        timeout: 1000,
      });
    }
  };

  return {
    checkouts,
    loading,
    fetchCheckouts,
    postHistoryData,
  };
}
