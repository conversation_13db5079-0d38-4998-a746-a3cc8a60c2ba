import { useLocalStorage } from "@vueuse/core";
import { v4 as uuidv4 } from "uuid";

type adminType = {
  data: {
    status: number;
  };
};
export function useAdmin() {
  const { $infoToast, $warningToast } = useNuxtApp();
  const pending = ref(false);
  const { table } = useTable();
  let UUID = useLocalStorage("UUID", uuidv4());
  const { t } = useI18n();

  const checkSession = async () => {
    pending.value = true;
    const response = await apiPost<adminType>("/a", {
      table_id: table?.value?.id,
      uuid: UUID.value,
    });

    const isAdmin = response?.data?.value?.data?.status === 1 ? true : false;
    const { openModal } = useModal();
    if (response?.error?.value) {
      $warningToast(t("errorOccured"));
    } else {
      if (isAdmin) {
        $infoToast(t("youAreAdmin"));
      } else {
        openModal("userStatus");
      }
    }

    pending.value = false;

    return isAdmin;
  };

  const adminRequest = async () => {
    pending.value = true;
    const { data, error } = await apiPost<adminType>("/r", {
      table_id: table?.value?.id,
      uuid: UUID,
    });

    if (error?.value) {
      $warningToast(t("errorOccured"));
    } else {
      $infoToast(t("requestSentSuccessfully"));
    }
    pending.value = false;
  };

  const adminAprove = async () => {
    pending.value = true;
    const requestUUID = useState("requestUUID");
    const { data, error } = await apiPut<adminType>(`/a/${requestUUID.value}`, {
      uuid: UUID,
    });

    const status = data?.value?.data.status;
    const adminRequest = useState("adminRequested");

    if (error?.value) {
      $warningToast(t("errorOccured"));
    } else {
      if (status) {
        $infoToast(t("AdminTransferred"));
        adminRequest.value = false;
      } else {
        $infoToast(t("AdminNotTransferred"));
      }
    }

    return { data, error };
    pending.value = false;
  };

  return { adminRequest, checkSession, adminAprove, pending };
}
