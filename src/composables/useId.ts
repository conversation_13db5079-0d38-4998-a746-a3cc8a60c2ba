import { useLocalStorage } from "@vueuse/core";

export function useId() {
  const route = useRoute();
  const id = useLocalStorage<string | null>("mnu-fesvi-table-id", () => "");
  const encodedID = ref<string | null>(null);
  const error = ref<string | null>(null);

  watchEffect(() => {
    if (typeof route.params.id === "string") {
      try {
        id.value = route.params.id;
        encodedID.value = atob(id.value);
        error.value = "";
      } catch (e) {
        error.value = "Failed to decode id";
      }
    } else {
      error.value = "id is not found";
    }
  });

  return { encodedID, id, error };
}
