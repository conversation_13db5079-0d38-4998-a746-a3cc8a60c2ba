import {
  USER_COOKIE,
  User,
  type UserWithoutPassword,
} from "../types/authTypes";
import { onMounted, watchEffect } from "vue";

export default function useAuthUser() {
  const config = useRuntimeConfig();
  const userCookie = useCookie<User>(USER_COOKIE, {
    maxAge: 36000,
    domain: config.public.cookieDomain as string,
    sameSite: "lax",
  });
  const user = ref<UserWithoutPassword | null>(null);

  const updateUserFromCookie = () => {
    const cookieValue = userCookie.value;
    if (
      cookieValue &&
      Object.keys(cookieValue).length > 0 &&
      cookieValue.token != ""
    ) {
      user.value = cookieValue;
    } else {
      user.value = null;
    }
  };

  // Watch for changes in the cookie
  watchEffect(() => {
    updateUserFromCookie();
  });

  return user;
}
