import { first } from "lodash";
import {
  USER_COOKIE,
  LoginResponse,
  LogoutResponse,
  UserUpdateRequest,
  User,
} from "../types/authTypes";
import useAuthUser from "./useAuthUser";

export default function useAuthMethods() {
  const authUser = useAuthUser();
  const config = useRuntimeConfig();
  const { auth } = useSupabaseClient();

  const setUser = (user: any) => {
    authUser.value = user;
    const userCookie = useCookie(USER_COOKIE, {
      maxAge: 36000,
      domain: config.public.cookieDomain as string,
      sameSite: "lax",
    });
    userCookie.value = JSON.stringify(user);
  };

  const register = async (
    first_name: string,
    last_name: string,
    email: string,
    password: string,
    phone: string
  ) => {
    try {
      const url = new URL(`/client/register`, config.public.capiBase).href;
      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ first_name, last_name, email, password, phone }),
      });

      const data = await response.json();

      if (data.status) {
        setUser({ ...data.data.client, token: data.data.token });
      }

      return {
        status: data.status,
        message: data.message,
        data: authUser,
      };
    } catch (error) {
      return { status: false, message: "Registration Failed" };
    }
  };

  const fetchAndLoginOauthUser = async (
    email: string
  ): Promise<{
    status: boolean;
    message: string;
    data?: User | null;
  }> => {
    try {
      const url = new URL(`/client/email`, config.public.capiBase).href;
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${config.public.adminToken}`,
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.status) {
        setUser({ ...data.data.client, token: data.data.token });
      }

      return {
        status: data.status,
        message: data.message,
        data: authUser.value,
      };
    } catch (error) {
      return { status: false, message: "User fetch failed" };
    }
  };

  const registerWithOAuth = async (
    first_name: string,
    last_name: string,
    email: string,
    phone: string,
    provider: string,
    oauth_token: string,
    refresh_token: string
  ) => {
    try {
      const url = new URL(`/client/register`, config.public.capiBase).href;
      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          first_name: first_name ? first_name : first(email.split("@")),
          last_name: last_name ? last_name : "",
          email,
          phone,
          provider,
          provider_id: provider,
          oauth_token,
          refresh_token,
        }),
      });

      const data = await response.json();

      if (data.status) {
        setUser({ ...data.data.client, token: data.data.token });
      }

      return {
        status: data.status,
        message: data.message,
        data: authUser,
      };
    } catch (error) {
      return { status: false, message: "Registration Failed" };
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const url = new URL(`/client/login`, config.public.capiBase).href;
      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (data.status) {
        setUser({ ...data.data.client, token: data.data.token });
      }

      return {
        status: data.status,
        message: data.message,
        data: authUser,
      };
    } catch (error) {
      return { status: false, message: "Login Failed" };
    }
  };

  const logout = async () => {
    // other cookie definitions...

    if (authUser.value && authUser.value.token) {
      try {
        const url = new URL(`/client/logout`, config.public.capiBase).href;
        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${authUser.value.token}`,
          },
          body: JSON.stringify({}),
        });
        setUser(null);

        await auth.signOut();
        return { status: true, message: "Logout Successful" };
      } catch (error) {
        return { status: false, message: "Logout Failed" };
      } finally {
        setUser(null);
      }
    } else {
      setUser({});
      return { status: true, message: "Already logged out" };
    }
  };
  const updateProfile = async (updatedData: UserUpdateRequest) => {
    if (!authUser.value || !authUser.value.token) {
      return { status: false, message: "User not authenticated" };
    }

    try {
      const userId = authUser.value.id; // Assuming the user's ID is stored in authUser
      const url = new URL(`/cp/client/${userId}`, config.public.capiBase).href;

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authUser.value.token}`,
        },
        body: JSON.stringify(updatedData),
      });

      const data = await response.json();

      if (data.status) {
        // Update the user data in the state if necessary
        setUser({ ...authUser.value, ...data.data });
      }

      return {
        status: data.status,
        message: data.message,
        data: authUser,
      };
    } catch (error) {
      return { status: false, message: "Update failed" };
    }
  };

  return {
    setUser,
    login,
    register,
    logout,
    updateProfile,
    registerWithOAuth,
    fetchAndLoginOauthUser,
  };
}
