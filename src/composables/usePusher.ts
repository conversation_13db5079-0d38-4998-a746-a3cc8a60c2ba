import Pusher<PERSON>ingleton from '~/utils/pusherSingleton';

export function usePusher() {
  const { id } = useId();
  const { table, restaurant } = useTable();

  if (typeof id.value !== "string") {
    throw createError({
      statusCode: 404,
      statusMessage: "Incorrect table id",
      fatal: true,
    });
  }

  const subscriptions = {
    new_order: false,
    cart_updated: false,
    checkout_completed: false
  };

  const orderSubscribe = async (callback: (data: any) => void) => {
    if (!process.client) return;

    const restaurantSlug = restaurant.value?.slug || "restaurant";
    const channel = PusherSingleton.getChannel(restaurantSlug);
    
    if (!channel) return;

    if (!subscriptions.new_order) {
      channel.bind("new_order", callback);
      subscriptions.new_order = true;
    }
  };

  const cartSubscribe = async (callback: (data: any) => void) => {
    if (!process.client) return;

    const restaurantSlug = restaurant.value?.slug || "restaurant";
    const channel = PusherSingleton.getChannel(restaurantSlug);
    
    if (!channel) return;

    if (!subscriptions.cart_updated) {
      channel.bind("cart_updated", callback);
      subscriptions.cart_updated = true;
    }
  };

  const checkoutSubscribe = async (callback: (data: any) => void) => {
    if (!process.client) return;

    const restaurantSlug = restaurant.value?.slug || "restaurant";
    const channel = PusherSingleton.getChannel(restaurantSlug);
    
    if (!channel) return;

    if (!subscriptions.checkout_completed) {
      channel.bind("checkout", callback);
      subscriptions.checkout_completed = true;
    }
  };

  const getChannel = () => {
    if (!process.client) return null;
    const restaurantSlug = restaurant.value?.slug || "restaurant";
    return PusherSingleton.getChannel(restaurantSlug);
  };

  return {
    orderSubscribe,
    cartSubscribe,
    checkoutSubscribe,
    channel: getChannel(),
  };
}
