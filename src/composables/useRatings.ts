import { StorageSerializers, useLocalStorage } from "@vueuse/core"; // Assuming you're using VueUse library for localStorage
import { Rating, RatingData } from "src/types/commonTypes";

export function useRatings() {
  const { table, restaurant } = useTable();
  const { id } = useId();
  const { $warningToast } = useNuxtApp();
  const { t } = useI18n();
  const pending = ref(false);

  const ratings: globalThis.Ref<Rating[] | null> = useState(
    "ratings",
    () => null
  );

  // Initialize local storage reference at the top level
  const storedRatings = useLocalStorage<Rating[]>(`t/${id.value}/ratings`, [], {
    serializer: StorageSerializers.object,
  });

  // Load ratings from local storage
  const loadRatings = () => {
    ratings.value = storedRatings.value;
  };

  // Function to send rating to the API
  const sendRating = async (dishId: number, ratingValue: number) => {
    try {
      pending.value = true;
      const response = await apiPost<RatingData>("/rating", {
        rate: ratingValue,
        restaurant: restaurant.value?.slug,
        table_id: table.value?.id,
        dish_id: dishId,
      });

      // Update local storage and local state with new rating
      if (response.data.value?.status) {
        const newRating = response.data.value?.data;
        const updatedRatings = ratings.value
          ? [...ratings.value, newRating]
          : [newRating];
        ratings.value = updatedRatings as Rating[];
        storedRatings.value = [...storedRatings.value, newRating as Rating];
      } else {
        // Handle the error case
        throw Error("Unable to update ratings");
      }
    } catch (error) {
      $warningToast(t("canNotUpdateRatings"));
    } finally {
      pending.value = false;
    }
  };

  return {
    ratings,
    loadRatings,
    pending,
    sendRating,
  };
}
