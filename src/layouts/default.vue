<script setup>
import { ref, computed } from "vue";

// const messages = []
// const restaurant = ref({
//   title: "Spice Hut Indian Restaurant",
//   address: "2036 2ND AVE, NEW YORK, NY 10029",
//   hours: { open: "10:00AM", close: "11:00PM" },
//   categories: [
//     { id: 1, title: "North Indian" },
//     { id: 2, title: "Chinese" },
//     { id: 3, title: "Fast Food" },
//     { id: 4, title: "South Indian" },
//   ],
//   ratings: [
//     {
//       id: 1,
//       author: [],
//       rate: 5,
//       review: "Here is the review",
//       date: new Date(),
//     },
//     { id: 2, author: [], rate: 3, review: "", date: new Date() },
//     { id: 3, author: [], rate: 5, review: "", date: new Date() },
//     { id: 4, author: [], rate: 2, review: "", date: new Date() },
//     { id: 5, author: [], rate: 5, review: "", date: new Date() },
//     { id: 6, author: [], rate: 4, review: "", date: new Date() },
//     { id: 7, author: [], rate: 5, review: "", date: new Date() },
//     { id: 8, author: [], rate: 1, review: "", date: new Date() },
//     { id: 9, author: [], rate: 5, review: "", date: new Date() },
//   ],
// });
// const ordered_items = [
//   { id: 1, title: "ხაში", price: 1, quantity: 1, icon: "success" },
//   { id: 2, title: "ბოზბაში", price: 314, quantity: 1, icon: "danger" },
// ];
// const categories = [
//   { id: 1, title: "Burgers", image: "img/list/1.png", total_items: 5 },
//   { id: 2, title: "Sandwiches", image: "img/list/2.png", total_items: 8 },
//   { id: 3, title: "Soups", image: "img/list/3.png", total_items: 3 },
//   { id: 4, title: "Pizzas", image: "img/list/4.png", total_items: 56 },
//   { id: 5, title: "Pastas", image: "img/list/5.png", total_items: 10 },
//   { id: 6, title: "Chinese", image: "img/list/6.png", total_items: 25 },
// ];
// const promos = [
//   {
//     id: 1,
//     title: "Bite me sandwiches",
//     image: "img/list/7.png",
//     categories: [
//       { id: 1, title: "North Indian" },
//       { id: 2, title: "Indian" },
//     ],
//     new: 1,
//     price: 550,
//   },
//   {
//     id: 2,
//     title: "Famous Daves Bar",
//     image: "img/list/8.png",
//     categories: [
//       { id: 1, title: "North Indian" },
//       { id: 2, title: "Indian" },
//     ],
//     new: 0,
//     price: 550,
//   },
//   {
//     id: 3,
//     title: "Great chixirtmas",
//     image: "img/list/9.png",
//     categories: [
//       { id: 1, title: "North Indian" },
//       { id: 2, title: "Indian" },
//     ],
//     new: 1,
//     price: 550,
//   },
// ];
// const dishes = [
//   {
//     id: 11,
//     title: "Chicken Tikka Sub",
//     price: 60,
//     quantity: 1,
//     icon: "img/4.jpg",
//     label: { color: "success", title: "Pure Veg" },
//   },
//   {
//     id: 12,
//     title: "Veg Spring Roll",
//     price: 14,
//     quantity: 1,
//     icon: "img/5.jpg",
//     label: { color: "danger", title: "BESTSELLER" },
//   },
//   {
//     id: 13,
//     title: "Stuffed Mushroom",
//     price: 21,
//     quantity: 1,
//     icon: "img/2.jpg",
//     label: { color: "warning", title: "SALE" },
//   },
//   {
//     id: 14,
//     title: "Stuffed Mushroom",
//     price: 67,
//     quantity: 1,
//     icon: "img/3.jpg",
//     label: { color: "primary", title: "Great Price" },
//   },
// ];

// const add_item = (item) => {
//   ordered_items.push(item);
// };
// const getRestaurant = async () => {
//   let restaurant = await this.$axios.get("/restaurant/some-restaurant");
//   restaurant = restaurant.data.data;
// };
// const order_placed = (data) => {
//   messages.push(JSON.stringify(data));
// };

// const order_total = computed(() => {
//   return ordered_items.reduce((p, n) => p + n.price * n.quantity, 0);
// });

// computed: {
//   order_total() {
//     return this.ordered_items.reduce( (p, n) => p + ( n.price * n.quantity ), 0);
//   },
// },
// methods: {
//   add_item( item ) {
//     this.ordered_items.push( item );
//   },
//   async getRestaurant() {
//     let restaurant = await this.$axios.get( '/restaurant/some-restaurant' );
//     this.restaurant = restaurant.data.data;
//   },
//   order_placed( data ) {
//     this.messages.push( JSON.stringify( data ) );
//   }
// },
// mounted() {
//   // Pusher.logToConsole = true;
//   // let pusher = new Pusher(process.env.PUSHER_KEY, { cluster: process.env.PUSHER_CLUSTER });
//   // let channel = pusher.subscribe('my-channel');
//   //
//   // channel.bind('my-event', this.order_placed );
//
//   this.getRestaurant();
</script>
<template>
  <div>
    <slot />
  </div>
</template>
<style scoped>
body {
  background: #f3f7f8;
}
</style>
