<script setup lang="ts">
const loading = useLoading();
</script>

<template>
  <div class="font relative">
    <div class="sticky z-40 top-0 shadow" v-if="!loading">
      <TableHeader>
        <template #image>
          <slot name="headerImage"> </slot>
        </template>
        <template #title>
          <slot name="headerTitle"></slot>
        </template>
        <template #aside>
          <slot name="headerAside"></slot>
        </template>
      </TableHeader>
    </div>
    <!-- Skeleton Loader and Spinner -->
    <div v-if="loading">
      <!-- Skeleton Loader -->
      <SkeletonLoader class="absolute inset-0 z-40 mt-24 mx-12 md:mx-24" />

      <div
        class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center"
      >
        <!--<div class="text-center text-black">
          <i class="fa fa-spin fa-spinner fa-5x"></i>
        </div>-->
      </div>
    </div>
    <!-- Not Loader -->
    <div class="container px-3 mx-auto pb-32 pt-3" v-if="!loading">
      <slot />
    </div>
    <!-- <BackToTop /> -->
    <div class="fixed bottom-0 w-full shadow" v-if="!loading">
      <TableNavigation />
    </div>
  </div>
</template>

<style scoped>
.font {
  min-height: 100vh;
  font-family: "Noto Sans Georgian", "Quicksand", sans-serif;
}
</style>
