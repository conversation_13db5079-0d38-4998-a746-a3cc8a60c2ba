<script setup lang="ts">
import { useLocalStorage } from "@vueuse/core";
import Pusher<PERSON>ing<PERSON> from '~/utils/pusherSingleton';

const { id } = useId();
const { t } = useI18n();
const { $infoToast, $warningToast } = useNuxtApp();
const { getTable, table, restaurant } = useTable();
const { getCart, clearCart, cartDishes } = useCart();
const { getOrders, orders } = useOrder();
const { loadRatings } = useRatings();
const route = useRoute();
const router = useRouter();

const { isInitialized, provideTableLayoutState } = useTableLayoutState()
provideTableLayoutState()

const isUpdatingCart = useState('isUpdatingCart', () => false);

if (typeof id.value !== "string") {
  throw createError({
    statusCode: 404,
    statusMessage: "Incorrect table id",
    fatal: true,
  });
}

const { registerModal, openModal, closeModal } = useModal();

async function updateCartData() {
  if (isUpdatingCart.value) return; // Prevent concurrent updates
  
  try {
    isUpdatingCart.value = true;
    await getCart();
  } finally {
    isUpdatingCart.value = false;
  }
}

async function updateOrderData() {
  await getOrders();
}

async function updateRatingsData() {
  await loadRatings();
}

async function updateOrderAndCartData() {
  await updateOrderData();
  await updateCartData();
}

// Initialize data only once
onBeforeMount(async () => {
  if (id.value && !isInitialized.value) {
    loading.value = true;
    try {
      await Promise.all([
        updateCartData(),
        updateOrderData(),
        table.value ? null : getTable(id.value as string),
        updateRatingsData(),
      ]);
      isInitialized.value = true;
    } finally {
      loading.value = false;
    }
  }
});

// Setup Pusher only once when mounted
onMounted(async () => {
  if (process.client && table.value && restaurant.value) {
    const restaurantSlug = restaurant.value.slug || "restaurant";
    
    try {
      const channel = PusherSingleton.getChannel(restaurantSlug);
      
      if (channel) {
        channel.bind('pusher:subscription_succeeded', () => {
          channel.bind("new_order", updateOrderAndCartData);
          channel.bind("cart_updated", updateCartData);
          channel.bind("checkout", updateOrderData);
        });
      }
    } catch (error) {
      // Handle error silently
    }
  }
});

// Cleanup on unmount
onUnmounted(() => {
  if (process.client && restaurant.value) {
    const restaurantSlug = restaurant.value.slug || "restaurant";
    PusherSingleton.unsubscribe(restaurantSlug);
  }
});

const loading = useLoading();

// Provide the initialized state to child components
provide('tableLayoutInitialized', isInitialized);
</script>

<template>
  <div class="font relative">
    <div class="sticky z-40 top-0 shadow" v-if="!loading">
      <TableHeader>
        <template #image>
          <slot name="headerImage"> </slot>
        </template>
        <template #title>
          <slot name="headerTitle"></slot>
        </template>
        <template #aside>
          <slot name="headerAside"></slot>
        </template>
      </TableHeader>
    </div>
    <!-- Skeleton Loader and Spinner -->
    <div v-if="loading">
      <!-- Skeleton Loader -->
      <SkeletonLoader class="absolute inset-0 z-40 mt-24 mx-12 md:mx-24" />

      <div
        class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center"
      >
        <!--<div class="text-center text-black">
          <i class="fa fa-spin fa-spinner fa-5x"></i>
        </div>-->
      </div>
    </div>
    <!-- Not Loader -->
    <div class="container px-3 mx-auto pb-32 pt-3" v-if="!loading">
      <slot />
    </div>
    <!-- <BackToTop /> -->
    <div class="fixed bottom-0 w-full shadow" v-if="!loading">
      <TableNavigation />
    </div>
  </div>
</template>

<style scoped>
.font {
  min-height: 100vh;
  font-family: "Noto Sans Georgian", "Quicksand", sans-serif;
}
</style>
