<script setup lang="ts">
import { Input, Checkbox, Alert } from "flowbite-vue";
import CashIcon from "~/components/icons/CashIcon.vue";
import { useToast } from "vue-toastification";
import { Checkout } from "~/types/commonTypes";
const toast = useToast();
const { query } = useRoute();
let { tips } = query;
const { capitalizeFirstLetter } = useHelpers();
const { $warningToast, $successToast } = useNuxtApp();

const { orderedDishesPrice, orders, discount, discountedTotal } = useOrder();
const { checkout } = useCheckout();
const { restaurant } = useTable();
const user = useAuthUser();
const { postHistoryData } = useHistory();
const loading = useLoading();
const config = useRuntimeConfig();

onMounted(() => {
  loading.value = false;
});

const feeAmont = computed((): number => {
  if (restaurant.value?.fee) {
    return Number((restaurant.value?.fee / 100) * orderedDishesPrice.value);
  } else {
    return 0;
  }
});
const encodedCheckoutId = ref("");
const showorderRecievModal = ref(false);
const haveExactAmount = ref(false);
const amount = ref(String(discountedTotal.value));

const makeCheckout = async () => {
  loading.value = true;
  let checkoutResponseData: Partial<Checkout> = {};
  let checkoutResponseClientIds: string = "";
  if (haveExactAmount.value) {
    const { error, data, client_ids } = await checkout(
      "cash",
      Number(tips) || 0,
      discount.value?.code === undefined ? undefined : null
    );
    if (!error.value) {
      showorderRecievModal.value = true;
    } else {
      return;
    }

    const checkoutId = data?.value?.data?.id;
    encodedCheckoutId.value = btoa(String(checkoutId || ""));
    checkoutResponseData = data?.value?.data as Checkout;

    checkoutResponseClientIds = client_ids as string;
  } else {
    if (Number(amount.value) < discountedTotal.value) {
      toast.error("incorrectAmount");
      return;
    }
    const { error, data, client_ids } = await checkout(
      "cash",
      Number(tips) || 0,
      discount.value?.code === undefined ? undefined : null,
      amount.value
    );
    if (!error.value) {
      showorderRecievModal.value = true;
    } else {
      return;
    }

    const checkoutId = data?.value?.data?.id;
    checkoutResponseData = data?.value?.data as Checkout;
    encodedCheckoutId.value = btoa(String(checkoutId || ""));
    checkoutResponseClientIds = client_ids as string;
  }
  discount.value = null;
  discountedTotal.value = 0;
  if (
    encodedCheckoutId.value &&
    user.value &&
    user.value.token &&
    checkoutResponseClientIds
  ) {
    const response = await postHistoryData({
      client_ids: checkoutResponseClientIds,
      order_details: new URL(
        `/c/${encodedCheckoutId.value}`,
        config.public.siteUrl
      ).toString(),
      restaurant_id: checkoutResponseData?.restaurant_id,
      amount: checkoutResponseData?.amount,
      quantity: checkoutResponseData?.quantity,
      tip: checkoutResponseData?.tip,
      restaurant_name: restaurant.value?.title,
      restaurant_logo: restaurant.value?.logo,
    });
    if (!response.status) {
      $warningToast("Failed to save history data: " + response.message);
    }
  }

  loading.value = false;
};

const goToReceiptPage = async () => {
  loading.value = true;

  if (encodedCheckoutId) {
    await navigateTo({
      path: `/c/${encodedCheckoutId.value}`,
    });
  }
};
</script>

<template>
  <NuxtLayout name="table-layout">
    <template #headerTitle> {{ capitalizeFirstLetter($t("order")) }} </template>
    <main v-if="orders?.length !== 0" class="flex flex-col items-center gap-5">
      <CashIcon />
      <p class="font-bold mt-4">{{ $t("howMuchCashUse") }}</p>
      <p class="text-center font-medium text-cocoa">
        {{ $t("notifyTheWaiter") }}
      </p>
      <div>
        <div class="flex items-center gap-4">
          <Input
            type="number"
            class="text-center text-xl w-32 caret-red-700 bg-white rounded-xl py-3 text-gray-400 font-medium placeholder-gray-300 focus:border-velvet focus:outline-none focus:ring-velvet"
            v-model="amount"
          >
          </Input>
          <p class="p-3 px-4 font-medium text-lg border rounded-xl">
            {{ restaurant?.currency?.symbol }}
          </p>
        </div>
        <Checkbox
          class="mt-3 font-medium text-gray-500 text-sm"
          :value="haveExactAmount"
          >{{ " " + $t("haveExactAmount") + discountedTotal + " ₾ " }}</Checkbox
        >
      </div>
      <Button @click="makeCheckout" class="mt-10 py-4 w-full bg-velvet"
        ><span class="font-semibold">{{ $t("confirm") }}</span></Button
      >
    </main>
    <Alert v-else type="info">{{ $t("noOrder") }}</Alert>
    <OrderRecievedModal
      :isShowModal="showorderRecievModal"
      @clickOut="showorderRecievModal = false"
      @onReceiptClick="goToReceiptPage"
    />
  </NuxtLayout>
</template>
