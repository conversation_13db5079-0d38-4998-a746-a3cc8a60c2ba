<script setup lang="ts">
import { Category, CategoryList } from "types/commonTypes";
import { Item } from "types/table-types";

const { searchedItems, items } = useTable();
const { sortedCategories } = useTable();
const { query } = useRoute();
let { category } = query;
const { capitalizeFirstLetter } = useHelpers();
let promoItems = ref<Item[]>([]);

const searchValue = ref("");
const selectedCat = ref<CategoryList>();

// setTimeout(() => {
//   searchValue.value = typeof category === "string" ? `#${category}` : "";
// }, 0);

const loading = useLoading();

onMounted(() => {
  if (category) {
    for (let cat of sortedCategories.value) {
      if (cat.data?.id == Number(category as string)) {
        selectedCat.value = cat;
        searchValue.value = `#${cat.name}`;
      }
    }
  }
  loading.value = false;
});
onBeforeMount(() => {
  items.value?.forEach((dish) => {
    // Check if the dish has promos
    if (dish.promos && dish.promos.length > 0) {
      promoItems.value.push(dish);
    }
  });
});

const moveSelectedCategoryToFront = computed((): CategoryList[] => {
  const result = [];

  for (let cat of sortedCategories.value) {
    if (cat.data?.id == Number(category as string)) {
      result.unshift(cat);
    } else {
      result.push(cat);
    }
  }
  return result;
});

const naviageteAndSelectCategory = async (cat: CategoryList) => {
  if (cat.data) {
    await navigateTo({
      path: `menu`,
      query: {
        category: cat.data.id,
      },
      replace: true,
    });
  } else {
    await navigateTo({
      path: `menu`,
      query: {
        category: "",
      },
      replace: true,
    });
  }

  selectedCat.value = cat;

  searchValue.value = cat && cat.name ? `#${cat.name}` : `#`;
};
</script>

<template>
  <NuxtLayout name="table-layout">
    <template #headerTitle>{{ capitalizeFirstLetter($t("menu")) }} </template>
    <template #headerAside>
      <CategoryScrollBar
        class="mt-2"
        :categories="moveSelectedCategoryToFront"
        :selected="(selectedCat as CategoryList)"
        @on-category-select="
          (cat) => {
            naviageteAndSelectCategory(cat);
          }
        "
      />
    </template>
    <main>
      <DishSearch v-model="searchValue" />
      <PromoCarousel :items="promoItems" v-if="promoItems.length > 0" />
      <ItemCards :items="searchedItems(searchValue)" />
    </main>
  </NuxtLayout>
</template>
<style>
@keyframes moveShrinkFade {
  to {
    transform: translate(0, 0) scale(0.1);
    opacity: 0;
  }
}
</style>
