<script setup lang="ts">
import { Alert } from "flowbite-vue";
import { ref, computed, watch } from 'vue';

const { restaurant } = useTable();
const { addOrder, pending, orderedDishesPrice, getOrders } = useOrder();
const { cartDishes, cartDishesPrice, clearCart, getCart } = useCart();
const { $warningToast } = useNuxtApp();
const ordered = ref(false);
const { capitalizeFirstLetter } = useHelpers();
const userLocation = ref(null);
const isLocationRequired = ref(true);

const handleLocationSelected = (location) => {
  userLocation.value = location;
};

const isKeepzLoading = ref(false);

// Payment modal state
const showPaymentModal = ref(false);
const paymentUrl = ref('');
const currentOrderId = ref('');
const currentOrderAmount = ref(0);

const placeOrderAndClearCart = async () => {
  if (isLocationRequired.value && !userLocation.value) {
    $warningToast('Please share your location for delivery');
    return;
  }

  // Start order creation and payment process
  isKeepzLoading.value = true;

  try {
    // Calculate the total amount
    const totalAmount = PaymentSummaries.value.find(summary => summary.title === 'total')?.price || 0;

    // Prepare order data with dishes from cart
    const orderData = {
      dishes: cartDishes.value?.map(dish => ({
        id: dish.id,
        name: dish.title || dish.name,
        price: dish.price,
        quantity: dish.quantity
      })) || [],
      amount: totalAmount,
      tableId: null, // Add table ID if available
      clientId: null  // Add client ID if available
    };

    // Send request to create-order API
    const response = await fetch('/api/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(orderData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const orderResult = await response.json();

    if (orderResult.success && orderResult.urlForQR) {
      // Set payment modal data
      paymentUrl.value = orderResult.urlForQR;
      currentOrderId.value = orderResult.orderId;
      currentOrderAmount.value = orderResult.amount;

      // Show payment modal instead of redirecting
      showPaymentModal.value = true;

      // Clear cart after successful order creation
      await clearCart();

    } else {
      throw new Error('Invalid response from order creation API');
    }

  } catch (error) {
    console.error('Order creation error:', error);
    $warningToast('Order creation failed. Please try again.');
  } finally {
    isKeepzLoading.value = false;
  }
};

// Payment modal handlers
const closePaymentModal = () => {
  showPaymentModal.value = false;
  paymentUrl.value = '';
  currentOrderId.value = '';
  currentOrderAmount.value = 0;
};

const handlePaymentModalClose = () => {
  closePaymentModal();
  // Optionally redirect to orders page or show success message
  ordered.value = true;
};

const feeAmont = computed((): number => {
  if (restaurant.value?.fee) {
    return Number((restaurant.value?.fee / 100) * cartDishesPrice.value);
  } else {
    return 0;
  }
});

const calculatePaymentSummaries = () => {
  const cartTotal = cartDishes.value
    ? cartDishes.value.reduce((sum, dish) => {
        // Check if there is a promo and if newPrice is set in the first promo
        const effectivePrice =
          dish.promos &&
          dish.promos.length > 0 &&
          dish.promos[0] &&
          "newPrice" in dish.promos[0]
            ? dish.promos[0].newPrice
            : dish.price;

        return sum + (effectivePrice as number) * (dish.quantity ?? 0);
      }, 0)
    : 0;

  const orderedTotal =
    orderedDishesPrice.value +
    (restaurant.value?.fee
      ? Number((restaurant.value?.fee / 100) * orderedDishesPrice.value)
      : 0);
  const fee = restaurant.value?.fee
    ? Number((restaurant.value?.fee / 100) * cartTotal)
    : 0;
  const grandTotal = cartTotal + orderedTotal + fee;

  return [
    {
      title: "subTotal",
      price: cartTotal.toFixed(2),
    },
    {
      title: "previouslyOrdered",
      price: orderedTotal.toFixed(2),
    },
    {
      title: "serviceFee",
      price: fee.toFixed(2),
    },
    {
      title: "total",
      price: grandTotal.toFixed(2),
    },
  ];
};

const PaymentSummaries = ref(calculatePaymentSummaries());

watch([cartDishes, orderedDishesPrice, feeAmont], () => {
  PaymentSummaries.value = calculatePaymentSummaries();
});
const loading = useLoading();

onMounted(() => {
  loading.value = false;
});
</script>

<template>
  <NuxtLayout name="table-layout">
    <template #headerTitle>
      {{ capitalizeFirstLetter($t("basket")) }}
    </template>
    <main>
      <section v-if="!ordered">
        <ItemCards
          :items="cartDishes?.map(dish => ({ ...dish, sound: '' })) ?? []"
          :alert="$t('noDishes')"
          :showImage="false"
        />
      </section>

      <!-- Add Location Picker -->
      <section class="mt-6" v-if="cartDishesPrice && !ordered">
        <h3 class="text-lg font-semibold mb-4">{{ $t('deliveryLocation') }}</h3>
        <LocationPicker @location-selected="handleLocationSelected" />
      </section>

      <section class="mt-6" v-if="cartDishesPrice">
        <ul>
          <PaymentSummary
            v-for="(summary, index) in PaymentSummaries"
            :key="index"
            :title="summary.title"
            :price="summary.price"
          />
        </ul>
        <Button
          @click="placeOrderAndClearCart"
          :disabled="isKeepzLoading"
          :class="[
            'w-full mt-4 py-4 font-bold flex justify-center items-center',
            isKeepzLoading ? 'bg-gray-400 cursor-not-allowed' : 'bg-velvet'
          ]"
        >
          <div v-if="isKeepzLoading" class="flex items-center">
            <Icon name="svg-spinners:180-ring" class="mr-2" />
            <span class="font-semibold tracking-widest">Processing...</span>
          </div>
          <span v-else class="font-semibold tracking-widest">
            {{ $t("order") }}
          </span>
        </Button>
      </section>
      <section v-if="ordered">
        <Alert type="success">{{ $t("orderCreatedSuccessfully") }}</Alert>
      </section>
    </main>

    <!-- Payment Modal -->
    <PaymentModal
      :isShowModal="showPaymentModal"
      :paymentUrl="paymentUrl"
      :orderId="currentOrderId"
      :amount="currentOrderAmount"
      :isLoading="isKeepzLoading"
      @clickOut="closePaymentModal"
      @close="handlePaymentModalClose"
      @openInNewTab="closePaymentModal"
    />
  </NuxtLayout>
</template>
