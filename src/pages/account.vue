<script setup>
const router = useRouter();
const searchValue = ref("");
const { id } = useId();
// Create Sample user
definePageMeta({
  middleware: ["authenticated"],
});
const user = useAuthUser();
const loading = useLoading();
const { logout } = useAuthMethods();
const route = useRoute();

const navigate = async (url) => {
  if (route.path !== url) {
    loading.value = true;
  }
  await nextTick(); // ensure UI update
  navigateTo(url); // This is a method provided by Nuxt 3
};

const handleLogout = async () => {
  try {
    loading.value = true;
    await logout();
    navigate(`/login`);
  } catch (error) {
    navigate(`/login`);
  }
};

onMounted(() => {
  loading.value = false;
});

onBeforeMount(() => {
  loading.value = true;
});
</script>

<template>
  <NuxtLayout name="table-layout-auth">
    <template #headerImage>
      <Icon name="tabler:user" size="40" class="text-velvet" />
    </template>
    <template #headerTitle> {{ $t("account") }} </template>

    <main>
      <div
        class="flex flex-col justify-start items-start gap-9 w-full mx-auto p-4"
      >
        <div class="text-center">
          <span class="text-black text-xl font-normal font-sans"
            >{{ $t("hello") }} ,
          </span>
          <span class="text-amber-700 text-xl font-medium font-sans"
            >{{ user.first_name }}!</span
          >
        </div>

        <!-- Section 1: Account -->
        <div class="flex flex-col justify-start items-start gap-8 w-full">
          <div class="w-full">
            <div class="opacity-80 text-black text-base font-medium font-sans">
              {{ $t("account") }}
            </div>
            <nuxt-link
              :to="`/checkouts`"
              class="py-4 border-b border-black border-opacity-20 flex justify-between items-center"
            >
              <div class="flex justify-center items-center gap-3">
                <!-- Paper Icon-->
                <Icon name="tabler:clipboard" size="35" class="text-velvet" />
                <div
                  class="opacity-80 text-black text-base font-normal font-sans"
                >
                  {{ $t("orderHistory") }}
                </div>
              </div>
              <Icon name="tabler:chevron-right" size="25" class="text-velvet" />
            </nuxt-link>
          </div>

          <!-- Section 2: Personal Information -->
          <div class="w-full">
            <div class="opacity-80 text-black text-base font-medium font-sans">
              {{ $t("personalInformation") }}
            </div>
            <nuxt-link
              :to="`/profile`"
              class="py-4 border-b border-black border-opacity-20 flex justify-between items-center"
            >
              <div class="flex justify-center items-center gap-3">
                <!-- Paper Icon-->
                <Icon name="tabler:user" size="35" class="text-velvet" />
                <div
                  class="opacity-80 text-black text-base font-normal font-sans"
                >
                  {{ $t("profile") }}
                </div>
              </div>
              <Icon name="tabler:chevron-right" size="25" class="text-velvet" />
            </nuxt-link>
            <!-- <nuxt-link href="/payment-methods" class="block">
              <div
                class="py-4 border-b border-black border-opacity-20 flex justify-between items-center"
              >
                <div class="flex justify-center items-center gap-3">
                  <Icon name="tabler:wallet" size="35" class="text-velvet" />
                  <div
                    class="opacity-80 text-black text-base font-normal font-sans"
                  >
                    {{ $t("paymentMethods") }}
                  </div>
                </div>
                <Icon
                  name="tabler:chevron-right"
                  size="25"
                  class="text-velvet"
                />
              </div>
            </nuxt-link> -->
          </div>

          <!-- Section 3: Other -->
          <div class="w-full">
            <div class="opacity-80 text-black text-base font-medium font-sans">
              {{ $t("other") }}
            </div>
            <nuxt-link
              class="py-4 border-b cursor-pointer border-black border-opacity-20 flex justify-between items-center"
              @click="handleLogout"
            >
              <div class="flex justify-center items-center gap-3">
                <!-- Paper Icon-->
                <Icon name="tabler:logout" size="35" class="text-velvet" />
                <div
                  class="opacity-80 text-black text-base font-normal font-sans"
                >
                  {{ $t("logout") }}
                </div>
              </div>
              <Icon name="tabler:chevron-right" size="25" class="text-velvet" />
            </nuxt-link>
          </div>
        </div>
      </div>
    </main>
  </NuxtLayout>
</template>
