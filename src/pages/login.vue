<template>
  <NuxtLayout name="table-layout-auth">
    <template #headerImage>
      <Icon name="tabler:user" size="40" class="text-velvet" />
    </template>
    <template #headerTitle>
      {{ user && user.token ? $t("profile") : $t("signUp") }}
    </template>

    <div class="p-1">
      <!-- Tabs -->
      <div class="flex justify-center mb-8">
        <button
          v-if="!user || !user.token"
          @click="activeTab = 'login'"
          :class="tabButtonClasses('login')"
        >
          {{ $t("login") }}
        </button>
        <button
          v-if="!user || !user.token"
          @click="activeTab = 'register'"
          :class="tabButtonClasses('register')"
        >
          {{ $t("register") }}
        </button>
      </div>

      <div class="text-velvet text-center my-8">
        {{ $t("enterData") }}
      </div>
      <!-- Register Form -->
      <div v-if="activeTab === 'register' && !loading">
        <form @submit.prevent="submitRegister">
          <InputField
            label="firstName"
            v-model="registerForm.firstName"
            :error="registerFormErrors.firstName"
          />
          <InputField
            label="lastName"
            v-model="registerForm.lastName"
            :error="registerFormErrors.lastName"
          />
          <InputField
            label="email"
            v-model="registerForm.email"
            type="email"
            :error="registerFormErrors.email"
            :required="true"
          >
          </InputField>
          <InputField
            label="password"
            v-model="registerForm.password"
            type="password"
            :error="registerFormErrors.password"
            :required="true"
          />
          <InputField
            label="confirmPassword"
            v-model="registerForm.confirmPassword"
            type="password"
            :error="registerFormErrors.confirmPassword"
            :required="true"
          />
          <!-- <VueTelInput
            v-model="registerForm.phoneNumber.number"
            class="p-2 my-8"
            @on-input="onPhoneNumberChange"
            defaultCountry="ge"
          />
          <div
            v-if="registerFormErrors.phoneNumber"
            class="text-red-500 text-sm mb-4"
          >
            {{ registerFormErrors.phoneNumber }}
          </div> -->
          <div class="flex items-center mb-4">
            <input
              type="checkbox"
              v-model="registerForm.agreeToTermsAndConditions"
              class="h-6 w-6 text-velvet focus:ring-velvet border-gray-300 rounded"
              :class="
                registerFormErrors.agreeToTermsAndConditions
                  ? 'border-red-500'
                  : ''
              "
            />
            <div
              class="ml-2 text-gray-500"
              v-html="t('agreeToTermsAndConditions')"
            ></div>
          </div>
          <div
            v-if="registerFormErrors.agreeToTermsAndConditions"
            class="text-red-500 text-sm mb-4"
          >
            {{ registerFormErrors.agreeToTermsAndConditions }}
          </div>
          <div class="flex justify-center w-full">
            <Button
              class="flex justify-center items-center font-bold bg-velvet rounded-xl w-[100%] md:w-[25%] px-12 py-4"
              type="submit"
            >
              {{ $t("register") }}
            </Button>
          </div>
        </form>
      </div>

      <div v-if="activeTab === 'login' && !loading">
        <!-- Create a button to show google and facebook sign up using tailwind and icones.js.org -->
        <div
          class="flex flex-col md:flex-row md:space-x-4 justify-center md:space-y-0 space-y-4 my-8"
        >
          <button
            @click="mySignInHandler({ provider: 'google' })"
            class="px-2 py-4 w-full border border-velvet flex rounded-lg text-velvet font-bold justify-between items-center"
          >
            <div class="flex">
              <Icon
                name="akar-icons:google-contained-fill"
                size="25"
                class="mr-2"
              />
              <div class="text-gray-800">{{ $t("loginWithGoogle") }}</div>
            </div>
            <Icon
              name="tabler:chevron-right"
              size="20"
              class="justify-self-end"
            />
          </button>

          <button
            @click="mySignInHandler({ provider: 'facebook' })"
            class="px-2 py-4 w-full border border-velvet flex rounded-lg text-velvet justify-between font-bold items-center"
          >
            <div class="flex">
              <Icon name="ic:baseline-facebook" size="25" class="mr-2" />
              <div class="text-gray-800">{{ $t("loginWithFacebook") }}</div>
            </div>
            <Icon
              name="tabler:chevron-right"
              size="20"
              class="justify-self-end"
            />
          </button>
        </div>
        <div class="flex justify-center items-center my-8">
          <div class="w-[35%] h-[1px] bg-gray-300"></div>
          <div class="mx-4 text-gray-500">{{ $t("or") }}</div>
          <div class="w-[35%] h-[1px] bg-gray-300"></div>
        </div>
        <form class="flex flex-col" @submit.prevent="submitLogin">
          <InputField
            label="email"
            v-model="loginForm.email"
            type="email"
            :error="loginFormErrors.email"
            :isIcon="true"
          >
            <template #icon-area>
              <Icon
                name="tabler:mail"
                size="25"
                class="text-velvet mr-1 mt-[-0.5rem]"
              />
            </template>
          </InputField>
          <InputField
            label="password"
            v-model="loginForm.password"
            :error="loginFormErrors.password"
            type="password"
            :isIcon="true"
          >
            <template #icon-area>
              <Icon
                name="tabler:lock"
                size="25"
                class="text-velvet mr-1 mt-[-0.5rem]"
              />
            </template>
          </InputField>
          <nuxt-link class="text-velvet mb-4 self-end">
            {{ $t("forgotPassword") }}
          </nuxt-link>

          <div class="flex justify-center w-full">
            <Button
              class="flex justify-center items-center font-bold bg-velvet rounded-xl w-[100%] md:w-[25%] px-12 py-4"
              type="submit"
            >
              {{ $t("login") }}
            </Button>
          </div>
        </form>
      </div>
    </div>
  </NuxtLayout>
</template>
<script setup lang="ts">
import { UserUpdateRequest } from "../types/authTypes";
import {
  RegisterForm,
  RegisterFormErrors,
  LoginForm,
  LoginFormErrors,
} from "../types/formTypes";
import { VueTelInput } from "vue-tel-input";
import "vue-tel-input/vue-tel-input.css";

const { auth } = useSupabaseClient();
const loading = useLoading();
const { t } = useI18n();
const config = useRuntimeConfig();
const { $warningToast, $successToast } = useNuxtApp();
const { register, login, registerWithOAuth } = useAuthMethods();
const user = useAuthUser();
const supUser = useSupabaseUser();
const router = useRouter();
const { id } = useId();

definePageMeta({
  middleware: ["login"],
});

watchEffect(() => {
  if (user.value) {
    router.push("/account");
  }
  if (supUser.value && !user.value) {
    loading.value = true;
    router.push("/confirm");
  }
});

const tabButtonClasses = (tab: string): string[] => {
  return [
    "px-6 py-2 w-full border-b",
    activeTab.value === tab ? "text-velvet border-velvet" : "text-gray-800",
  ];
};

const registerForm = ref<RegisterForm>({
  firstName: "",
  lastName: "",
  email: "",
  password: "",
  confirmPassword: "",
  phoneNumber: { number: "" },
  agreeToTermsAndConditions: false,
});
const registerFormErrors = ref<RegisterFormErrors>({
  firstName: "",
  lastName: "",
  email: "",
  password: "",
  confirmPassword: "",
  phoneNumber: "",
  agreeToTermsAndConditions: "",
});

const loginForm = ref<LoginForm>({
  email: "",
  password: "",
});
const loginFormErrors = ref<LoginFormErrors>({ email: "", password: "" });

const mySignInHandler = async ({ provider }: { provider: string }) => {
  try {
    const redirectUrl = new URL(`/confirm`, config.public.siteUrl).toString();

    const { data, error } = await auth.signInWithOAuth({
      provider: provider == "facebook" ? "facebook" : "google",
      options: {
        redirectTo: redirectUrl,
      },
    });
  } catch (error) {
    console.error("Sign-in failed:", error);
  }
};

const validateRegisterForm = (): boolean => {
  let isValid = true;

  // Validate First Name
  if (registerForm.value.firstName && registerForm.value.firstName.length < 2) {
    registerFormErrors.value.firstName = t("errorFirstNameLength");
    isValid = false;
  }

  //Validate Last Name similarly
  if (registerForm.value.lastName && registerForm.value.lastName.length < 2) {
    registerFormErrors.value.lastName = t("errorLastNameLength");
    isValid = false;
  }

  // Validate Email
  if (!registerForm.value.email) {
    registerFormErrors.value.email = t("errorEmailRequired");
    isValid = false;
  } else if (!isValidEmail(registerForm.value.email)) {
    registerFormErrors.value.email = t("errorInvalidEmail");
    isValid = false;
  } else {
    registerFormErrors.value.email = "";
  }

  // Validate Password
  if (!registerForm.value.password) {
    registerFormErrors.value.password = t("errorPasswordRequired");
    isValid = false;
  } else if (registerForm.value.password.length < 8) {
    registerFormErrors.value.password = t("errorPasswordLength");
    isValid = false;
  } else {
    registerFormErrors.value.password = "";
  }

  // Validate Confirm Password
  if (!registerForm.value.confirmPassword) {
    registerFormErrors.value.confirmPassword = t(
      "errorConfirmPasswordRequired"
    );
    isValid = false;
  } else if (
    registerForm.value.password !== registerForm.value.confirmPassword
  ) {
    registerFormErrors.value.confirmPassword = t("errorPasswordMismatch");
    isValid = false;
  } else {
    registerFormErrors.value.confirmPassword = "";
  }
  // // Validate Phone Number
  // if (!registerForm.value.phoneNumber.number) {
  //   registerFormErrors.value.phoneNumber = t("errorPhoneNumberRequired");
  //   isValid = false;
  // } else if (!isValidPhoneNumber(registerForm.value.phoneNumber.number)) {
  //   registerFormErrors.value.phoneNumber = t("errorInvalidPhoneNumber");
  //   isValid = false;
  // } else {
  //   registerFormErrors.value.phoneNumber = "";
  // }

  if (!registerForm.value.agreeToTermsAndConditions) {
    $warningToast(t("errorAgreeToTermsAndConditions"));
    registerFormErrors.value.agreeToTermsAndConditions = t(
      "errorAgreeToTermsAndConditions"
    );
    isValid = false;
  } else {
    registerFormErrors.value.agreeToTermsAndConditions = "";
  }

  return isValid;
};

// Utility function to validate email
function isValidEmail(email: string): boolean {
  const re = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return re.test(email.toLowerCase());
}

function isValidPhoneNumber(phoneNumber: string): boolean {
  // This regex attempts to validate international phone numbers more strictly.
  // Note: No regex can cover all valid formats perfectly.
  const re = /^\+(?:[0-9] ?){6,14}[0-9]$/;
  return re.test(phoneNumber);
}

const submitRegister = async (): Promise<void> => {
  if (!validateRegisterForm()) {
    $warningToast("Please fill all fields correctly");
    return;
  }

  try {
    loading.value = true;
    const response = await register(
      registerForm.value.firstName,
      registerForm.value.lastName,
      registerForm.value.email,
      registerForm.value.password,
      registerForm.value.phoneNumber.number
    );
    if (response.status) {
      $successToast("Registered successfully");
      router.push(`/account`);
    } else {
      $warningToast(response.message);
    }
  } catch (error) {
    $warningToast("Registration failed");
  } finally {
    loading.value = false;
  }
};

const validateLoginForm = (): boolean => {
  let isValid = true;
  if (!loginForm.value.email) {
    loginFormErrors.value.email = t("errorEmailRequired");
    isValid = false;
  } else {
    loginFormErrors.value.email = "";
  }

  if (!loginForm.value.password) {
    loginFormErrors.value.password = t("errorPasswordRequired");
    isValid = false;
  } else {
    loginFormErrors.value.password = "";
  }

  return isValid;
};

const submitLogin = async (): Promise<void> => {
  if (!validateLoginForm()) {
    $warningToast("Please enter valid credentials");
    return;
  }

  try {
    loading.value = true;
    const response = await login(
      loginForm.value.email,
      loginForm.value.password
    );
    if (response.status) {
      $successToast("Logged in successfully");
      if (id.value) {
        navigateTo("/t/" + id.value);
      } else {
        navigateTo("/account");
      }
    } else {
      $warningToast(response.message);
    }
  } catch (error) {
    $warningToast("Login failed");
  } finally {
    loading.value = false;
  }
};

const onPhoneNumberChange = (number: string, phoneObject: any): void => {
  registerForm.value.phoneNumber = phoneObject;
};

const activeTab = ref("login");

onMounted(() => {
  loading.value = false;
});
</script>

<style scoped>
/* Additional styling if needed */
</style>
