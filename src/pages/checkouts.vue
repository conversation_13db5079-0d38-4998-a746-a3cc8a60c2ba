<template>
  <NuxtLayout name="table-layout-auth">
    <template #headerImage>
      <Icon name="tabler:user" size="40" class="text-velvet" />
    </template>
    <template #headerTitle>
      {{ user && user.token ? $t("profile") : $t("signUp") }}
    </template>
    <div class="flex flex-col items-center justify-center" v-if="hasNoHistory">
      <img
        src="/img/empty_image.svg"
        alt="No History"
        class="w-80 h-60 mt-24"
      />
      <div class="text-velvet my-4 font-bold">{{ $t("thereAreNoOrders") }}</div>
      <nuxt-link :to="`/t/${id}`">
        <Button class="mt-4 px-4 py-2 bg-velvet">
          {{ $t("placeAnOrder") }}
        </Button>
      </nuxt-link>
    </div>
    <div class="space-y-4 m-1" v-else>
      <CheckoutCard
        v-for="checkout in checkouts"
        :key="checkout.id"
        :checkout="checkout"
      />
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: ["authenticated"],
});
const { checkouts, fetchCheckouts } = useHistory();
const user = useAuthUser();
const loading = useLoading();
const { id } = useId();

const hasNoHistory = computed(() => checkouts.value.length === 0);

onBeforeMount(() => {
  loading.value = true;
  if (!checkouts.value || checkouts.value.length === 0) {
    fetchCheckouts().finally(() => {
      loading.value = false;
    });
  }
});
</script>
