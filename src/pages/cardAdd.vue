<template>
  <div class="p-4 max-w-md mx-auto">
    <h2 class="text-2xl font-semibold mb-6">Add Payment Card</h2>
    <form @submit.prevent="submitCardDetails" class="space-y-4">
      <input-field
        label="Card Number"
        v-model="cardDetails.cardNumber"
        type="text"
        placeholder="1234 5678 9012 3456"
      />
      <div class="flex gap-4">
        <input-field
          label="Expiry Date"
          v-model="cardDetails.expiryDate"
          type="text"
          placeholder="MM/YY"
          class="flex-1"
        />
        <input-field
          label="CVV"
          v-model="cardDetails.cvv"
          type="text"
          placeholder="123"
          class="flex-1"
        />
      </div>
      <input-field
        label="Cardholder Name"
        v-model="cardDetails.cardholderName"
        type="text"
      />
      <submit-button label="Add Card" />
    </form>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: ["authenticated"],
});
// Define your reactive state
const cardDetails = reactive({
  cardNumber: "",
  expiryDate: "",
  cvv: "",
  cardholderName: "",
});

// Method to handle the submission of card details
const submitCardDetails = () => {
  // Implement proper validation and security measures
  // Example: console.log('Submitting:', cardDetails);
};
</script>
