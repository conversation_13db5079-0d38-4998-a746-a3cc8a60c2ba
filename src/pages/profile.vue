<template>
  <NuxtLayout name="table-layout-auth">
    <template #headerImage>
      <Icon name="tabler:user" size="40" class="text-velvet" />
    </template>
    <template #headerTitle>
      {{ user && user.token ? $t("profile") : $t("signUp") }}
    </template>

    <div class="p-4">
      <!-- Tabs -->

      <div class="flex justify-center mb-8" v-if="!loading">
        <button
          @click="activeTab = 'account'"
          :class="tabButtonClasses('account')"
        >
          {{ $t("account") }}
        </button>
        <button
          @click="activeTab = 'changePassword'"
          :class="tabButtonClasses('changePassword')"
        >
          {{ $t("changePassword") }}
        </button>
      </div>

      <!-- Update Form -->
      <div v-if="activeTab === 'account' && !loading">
        <form @submit.prevent="submitUpdate">
          <InputField
            label="firstName"
            v-model="profileForm.firstName"
            :error="profileFormErrors.firstName"
          />
          <InputField
            label="lastName"
            v-model="profileForm.lastName"
            :error="profileFormErrors.lastName"
          />
          <InputField
            label="email"
            v-model="profileForm.email"
            type="email"
            :error="profileFormErrors.email"
          />
          <VueTelInput
            v-model="profileForm.phoneNumber.number"
            class="p-2 my-8"
            @on-input="onPhoneNumberChange"
            defaultCountry="ge"
          />
          <div
            v-if="profileFormErrors.phoneNumber"
            class="text-red-500 text-sm mb-4"
          >
            {{ profileFormErrors.phoneNumber }}
          </div>
          <div class="flex justify-center w-full">
            <Button
              class="flex justify-center items-center font-bold bg-velvet rounded-xl w-[100%] md:w-[25%] px-12 py-4"
              type="submit"
            >
              {{ $t("submit") }}
            </Button>
          </div>
        </form>
      </div>

      <!-- Change Password Form -->
      <div v-if="activeTab === 'changePassword' && !loading">
        <form @submit.prevent="submitChangePassword">
          <InputField
            label="currentPassword"
            v-model="changePasswordForm.currentPassword"
            type="password"
            v-if="user && user.isPasswordSet"
            :error="changePasswordFormErrors.currentPassword"
          />
          <InputField
            label="newPassword"
            v-model="changePasswordForm.newPassword"
            type="password"
            :error="changePasswordFormErrors.newPassword"
          />
          <InputField
            label="confirmNewPassword"
            v-model="changePasswordForm.confirmNewPassword"
            type="password"
            :error="changePasswordFormErrors.confirmNewPassword"
          />
          <div class="flex justify-center w-full">
            <Button
              class="flex justify-center items-center font-bold bg-velvet rounded-xl w-[100%] md:w-[25%] px-12 py-4"
              type="submit"
            >
              {{ $t("submit") }}
            </Button>
          </div>
        </form>
      </div>
    </div>
  </NuxtLayout>
</template>
<script setup lang="ts">
import { UserUpdateRequest } from "../types/authTypes";
import {
  RegisterForm,
  RegisterFormErrors,
  ProfileForm,
  ProfileFormErrors,
  LoginForm,
  LoginFormErrors,
  ChangePasswordForm,
  ChangePasswordFormErrors,
} from "../types/formTypes";
import { VueTelInput } from "vue-tel-input";
import "vue-tel-input/vue-tel-input.css";

const loading = useLoading();
const { t } = useI18n();
const { $warningToast, $successToast } = useNuxtApp();
const { register, updateProfile, login } = useAuthMethods();
const user = useAuthUser();
const router = useRouter();
const { id } = useId();
const supUser = useSupabaseUser();

definePageMeta({
  middleware: ["authenticated"],
});

const tabButtonClasses = (tab: string): string[] => {
  return [
    "px-6 py-2 w-full border-b",
    activeTab.value === tab ? "text-velvet border-velvet" : "text-gray-800",
  ];
};

const profileForm = ref<ProfileForm>({
  firstName: "",
  lastName: "",
  email: "",
  phoneNumber: { number: "" },
});

const profileFormErrors = ref<ProfileFormErrors>({
  firstName: "",
  lastName: "",
  email: "",
  phoneNumber: "",
});

const changePasswordForm = ref<ChangePasswordForm>({
  currentPassword: "",
  newPassword: "",
  confirmNewPassword: "",
});

const changePasswordFormErrors = ref<ChangePasswordFormErrors>({
  currentPassword: "",
  newPassword: "",
  confirmNewPassword: "",
});

// Watch for changes in user data
watch(
  user,
  (newValue) => {
    if (newValue && newValue.token) {
      profileForm.value.firstName = newValue.first_name;
      profileForm.value.lastName = newValue.last_name;
      profileForm.value.email = newValue.email || "";
      profileForm.value.phoneNumber.number = newValue.phone || "";
    }
  },
  { immediate: true }
);

// Utility function to validate email
function isValidEmail(email: string): boolean {
  const re = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return re.test(email.toLowerCase());
}

function isValidPhoneNumber(phoneNumber: string): boolean {
  // This regex attempts to validate international phone numbers more strictly.
  // Note: No regex can cover all valid formats perfectly.
  const re = /^\+(?:[0-9] ?){6,14}[0-9]$/;
  return re.test(phoneNumber);
}

const onPhoneNumberChange = (number: string, phoneObject: any): void => {
  profileForm.value.phoneNumber = phoneObject;
};

const activeTab = ref(user.value && user.value.token ? "account" : "login");

const validateProfileUpdateForm = (): boolean => {
  let isValid = true;
  // Validate First Name
  if (!profileForm.value.firstName) {
    profileFormErrors.value.firstName = t("errorFirstNameRequired");
    isValid = false;
  } else {
    profileFormErrors.value.firstName = "";
  }

  // Validate Last Name
  if (!profileForm.value.lastName) {
    profileFormErrors.value.lastName = t("errorLastNameRequired");
    isValid = false;
  } else {
    profileFormErrors.value.lastName = "";
  }

  // Validate Email
  if (!profileForm.value.email) {
    profileFormErrors.value.email = t("errorEmailRequired");
    isValid = false;
  } else if (!isValidEmail(profileForm.value.email)) {
    profileFormErrors.value.email = t("errorInvalidEmail");
    isValid = false;
  } else {
    profileFormErrors.value.email = "";
  }

  if (
    profileForm.value.phoneNumber.number &&
    !isValidPhoneNumber(profileForm.value.phoneNumber.number)
  ) {
    profileFormErrors.value.phoneNumber = t("errorInvalidPhoneNumber");
    isValid = false;
  } else {
    profileFormErrors.value.phoneNumber = "";
  }

  return isValid;
};

const submitUpdate = async (): Promise<void> => {
  if (!validateProfileUpdateForm()) {
    $warningToast(t("pleaseEnterValidCredentials"));
    return;
  }

  try {
    loading.value = true;
    const response = await updateProfile({
      first_name: profileForm.value.firstName,
      last_name: profileForm.value.lastName,
      email: profileForm.value.email,
      phone: profileForm.value.phoneNumber.number,
    } as UserUpdateRequest);
    // Handle the response
    if (response.status) {
      $successToast(t("profileUpdatedSuccessfully"));
    } else {
      $warningToast(response.message);
    }
  } catch (error: any) {
    $warningToast("profileUpdateFailed");
  } finally {
    loading.value = false;
  }
};

const validateChangePasswordForm = (): boolean => {
  let isValid = true;

  // Validate Current Password
  if (
    user.value &&
    user.value.isPasswordSet &&
    !changePasswordForm.value.currentPassword
  ) {
    changePasswordFormErrors.value.currentPassword = t(
      "errorCurrentPasswordRequired"
    );
    isValid = false;
  } else {
    changePasswordFormErrors.value.currentPassword = "";
  }

  // Validate New Password
  if (!changePasswordForm.value.newPassword) {
    changePasswordFormErrors.value.newPassword = t("errorNewPasswordRequired");
    isValid = false;
  } else if (
    changePasswordForm.value.newPassword ==
    changePasswordForm.value.currentPassword
  ) {
    changePasswordFormErrors.value.newPassword = t(
      "errorNewPasswordSameAsCurrent"
    );
    isValid = false;
  } else if (changePasswordForm.value.newPassword.length < 8) {
    changePasswordFormErrors.value.newPassword = t("errorNewPasswordLength");
    isValid = false;
  } else {
    changePasswordFormErrors.value.newPassword = "";
  }

  // Validate Confirm New Password
  if (!changePasswordForm.value.confirmNewPassword) {
    changePasswordFormErrors.value.confirmNewPassword = t(
      "errorConfirmNewPasswordRequired"
    );
    isValid = false;
  } else if (
    changePasswordForm.value.newPassword !=
    changePasswordForm.value.confirmNewPassword
  ) {
    changePasswordFormErrors.value.confirmNewPassword = t(
      "errorNewPasswordMismatch"
    );
    isValid = false;
  } else {
    changePasswordFormErrors.value.confirmNewPassword = "";
  }

  return isValid;
};

const submitChangePassword = async (): Promise<void> => {
  if (!validateChangePasswordForm()) {
    $warningToast(t("pleaseEnterValidCredentials"));
    return;
  }

  try {
    loading.value = true;
    const response = await updateProfile({
      current_password: changePasswordForm.value.currentPassword,
      password: changePasswordForm.value.newPassword,
    });
    // Handle the response
    if (response.status) {
      $successToast(t("passwordChangedSuccessfully"));
      activeTab.value = "account";
    } else {
      $warningToast(response.message);
    }
    // Redirect or update UI as necessary
  } catch (error: any) {
    $warningToast(t("passwordChangeFailed"));
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loading.value = false;
});
</script>

<style scoped>
/* Additional styling if needed */
</style>
