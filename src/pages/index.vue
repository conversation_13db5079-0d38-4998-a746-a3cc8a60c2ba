<template>
  <!-- <section class="relative h-screen bg-cover bg-center" id="masthead">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="relative flex items-center h-full text-white">
      <h1
        class="text-[2.5rem] md:text-[3rem] lg:text-[70px] ml-16 lg:w-[35%] md:w-[50%] w-[75%]"
        v-html="$t('discoverGreatRestaurants')"
      ></h1>
    </div>
  </section>

  <section class="py-4 bg-white px-8">
    <div class="container mx-auto flex items-center flex-col">
      <div class="text-center w-full mb-8">
        <CustomHeading :text="$t('aboutUs')" />
      </div>
      <div class="flex flex-wrap -mx-4 text-[1.5rem] lg:text-[1.75rem]">
        <div class="w-full md:w-1/2 px-4 mb-8 md:mb-0">
          <h3 class="font-bold mb-4" id="subhead">{{ $t("whatsMenuHub") }}</h3>
          <p>
            {{ $t("welcomeToMNU") }}
          </p>
        </div>
        <div class="w-full md:w-1/2 px-4">
          <NuxtImg
            src="/img/Taking_Order.png"
            alt="About Us Image"
            class="w-full h-auto"
          />
        </div>
      </div>
    </div>
  </section>

  <section class="py-4 bg-white" id="howItWorks">
    <div class="container mx-auto">
      <CustomHeading :text="t('howItWorks')" />
      <div
        class="grid grid-cols-1 gap-8 my-8 mt-[8rem] text-[1rem] lg:text-[1.75rem]"
      >
        <div class="flex rounded p-4 icon-card">
          <div class="flex items-center flex-shrink-0 mx-4 p-1 lg:p-4">
            <NuxtImg
              src="/img/scan-search.svg"
              alt="About Us Image"
              class="w-full h-[5rem]"
            />
          </div>
          <div class="flex-grow p-4">
            {{ $t("browseRestaurants") }}
          </div>
        </div>

        <div class="flex rounded p-4 icon-card">
          <div class="flex-grow p-4">
            {{ $t("customizeYourOrder") }}
          </div>
          <div class="flex items-center flex-shrink-0 mx-4 p-1 lg:p-4">
            <NuxtImg
              src="/img/sliders-horizontal.svg"
              alt="About Us Image"
              class="w-full h-[5rem]"
            />
          </div>
        </div>

        <div class="flex rounded p-4 icon-card">
          <div class="flex items-center flex-shrink-0 mx-4 p-1 lg:p-4">
            <NuxtImg
              src="/img/sofa.svg"
              alt="About Us Image"
              class="w-full h-[5rem]"
            />
          </div>
          <div class="flex-grow p-4">
            {{ $t("chooseYourSetting") }}
          </div>
        </div>

        <div class="flex rounded p-4 icon-card">
          <div class="flex-grow p-4">
            {{ $t("exploreMore") }}
          </div>
          <div class="flex items-center flex-shrink-0 mx-4 p-1 lg:p-4">
            <NuxtImg
              src="/img/sparkles.svg"
              alt="About Us Image"
              class="w-full h-[5rem]"
            />
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="pb-16 bg-white px-8" id="contact">
    <div class="container mx-auto flex items-center flex-col">
      <div class="text-center w-full mb-8">
        <CustomHeading :text="$t('contactUs')" />
      </div>
      <div v-if="!isSubmitted && message" class="message success">
        {{ $t(message) }}
      </div>

      <div class="w-full md:w-2/3 lg:w-3/4" v-if="!isSubmitted">
        <form @submit.prevent="submitForm">
          <div class="flex items-center mb-2">
            <input
              type="text"
              name="name"
              v-model="form.name"
              :placeholder="t('name')"
              class="flex-grow rounded-lg p-2 mr-2 h-11"
              required
              :class="errors.name ? 'border-red-500' : 'border-[#979797]'"
            />
            <div class="border border-[#979797] rounded-lg p-2 h-11">
              <Icon name="ph:user" size="24" color="#000" />
            </div>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.name">
            {{ errors.name }}
          </p>

          <div class="flex items-center mb-2">
            <vue-tel-input
              v-model="form.phone"
              class="flex-grow rounded-lg mr-2 h-11"
              mode="international"
              inputOptions=""
              required
              :class="errors.phone ? 'border-red-500' : 'border-[#979797]'"
            ></vue-tel-input>
            <div class="border border-[#979797] rounded-lg p-2 h-11">
              <Icon name="ic:twotone-phone" size="24" color="#000" />
            </div>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.phone">
            {{ errors.phone }}
          </p>

          <div class="flex items-center mb-2">
            <input
              type="email"
              name="email"
              v-model="form.email"
              :placeholder="t('email')"
              class="flex-grow p-2 rounded-lg mr-2 h-11"
              required
              :class="errors.email ? 'border-red-500' : 'border-[#979797]'"
            />
            <div class="border border-[#979797] rounded-lg p-2 h-11">
              <Icon name="ic:outline-email" size="24" color="#000" />
            </div>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.email">
            {{ errors.email }}
          </p>

          <div class="mb-2">
            <textarea
              name="comments"
              v-model="form.comments"
              :placeholder="t('comments')"
              class="w-full p-2 border-[#979797] rounded-lg"
              rows="5"
              required
            ></textarea>
          </div>
          <p class="text-red-500 text-xs my-2" v-if="errors.comments">
            {{ errors.comments }}
          </p>

          <div class="w-full flex justify-center">
            <button
              type="submit"
              class="w-1/3 py-2 rounded-lg text-white bg-[#F37832] hover:bg-[#f26922]"
            >
              <Icon name="prime:send" class="mb-0.5" size="24" color="#fff" />

              {{ $t("submit") }}
            </button>
          </div>
        </form>
      </div>
      <div v-else>
        <NuxtImg src="/img/thank-you.svg" />
      </div>
    </div>
  </section> -->
</template>

<style>
.badge {
  transform: translate(50%, -50%);
}

#masthead {
  background-image: url("/img/master_image.jpg");
}

#subhead {
  color: var(--color-primary);
  font-size: 3rem;
  font-weight: 900;
}

.icon-card > div {
  border-radius: 1rem;
  background-color: rgba(243, 120, 50, 0.02);
  border-width: 2px;
  border-color: rgba(243, 120, 50, 0.2);
}
</style>
<script setup>
import { ref } from "vue";
import { VueTelInput } from "vue-tel-input";
import "vue-tel-input/vue-tel-input.css";

definePageMeta({ layout: "default", middleware: ["authenticated"] });

const config = useRuntimeConfig();
const user = useAuthUser();
const supUser = useSupabaseUser();
function replaceSubdomainWithMenuhub(url) {
  try {
    let newUrl = new URL(url);

    // Check for port, localhost, or invalid TLD
    if (
      newUrl.port ||
      newUrl.hostname === "localhost" ||
      !isValidTLD(newUrl.hostname)
    ) {
      throw new Error(
        "Invalid URL - contains port, is localhost, or lacks a valid TLD"
      );
    }

    // Split the hostname into parts
    let parts = newUrl.hostname.split(".");
    // Replace everything before the last dot with 'menuhub'
    if (parts.length >= 2) {
      parts = ["menuhub", parts[parts.length - 1]];
    } else {
      throw new Error("Invalid URL - lacks a valid TLD");
    }

    newUrl.hostname = parts.join(".");
    return newUrl.href;
  } catch (error) {
    throw error; // Propagate the error
  }
}

function isValidTLD(hostname) {
  let parts = hostname.split(".");
  return parts.length >= 2 && parts[parts.length - 1].length > 1;
}

onBeforeMount(() => {
  const route = useRoute();
  const router = useRouter();

  try {
    const urlPattern =
      /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;
    const newUrl = replaceSubdomainWithMenuhub(config.public.siteUrl);
    if (urlPattern.test(newUrl)) {
      // If it's a URL, redirect the user to the decoded URL
      window.location.href = newUrl;
    } else {
      throw new Error("Invalid Page");
    }
  } catch (error) {
    if (user.value) {
      router.push("/account");
    } else if (supUser.value && !user.value) {
      router.push("/confirm");
    } else {
      router.push("/login");
    }
  }
});

const { t } = useI18n();
const isSubmitted = ref(false);
const form = ref({
  name: "",
  phone: "",
  email: "",
  comments: "",
});
const errors = ref({ name: "", phone: "", email: "", comments: "" });

const validateForm = () => {
  errors.value = {};

  if (!form.value.name) {
    errors.value.name = "Name is required.";
  }

  const phonePattern = /^\+?(\d[\d-. ]+)?(\([\d-. ]+\))?[\d-. ]+\d$/;
  if (!phonePattern.test(form.value.phone)) {
    errors.value.phone = "Invalid Phone Number";
  }
  form.value.phone = form.value.phone.replace(/\s+/g, "");

  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailPattern.test(form.value.email)) {
    errors.value.email = "Please enter a valid email address.";
  }

  return Object.keys(errors.value).length === 0;
};

const submitForm = async () => {
  // Assuming you have these refs defined
  const message = ref("");

  if (!validateForm()) {
    message.value = "Validation failed. Please check your inputs.";
    return;
  }
  const apiUrl = config.public.apiBase + "/contact";

  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(form.value),
    });

    if (!response.ok) {
      // If HTTP status code is not OK
      throw new Error("Network response was not ok");
    }

    const responseData = await response.json();
    Object.keys(form.value).forEach((key) => {
      form.value[key] = "";
    });
    errors.value = {};
    message.value = "Form submitted successfully!";
    isSubmitted.value = true;
  } catch (error) {
    if (error.message === "Network response was not ok") {
      switch (error.response.status) {
        case 400:
          message.value = "Bad Request. Check your inputs.";
          break;
        case 500:
          message.value = "Server Error. Please try again later.";
          break;
        //... handle other status codes
        default:
          message.value = "An unexpected error occurred.";
      }
    } else {
      message.value =
        "Failed to submit. Please check your network connection or try again later.";
    }
  }
};
</script>
