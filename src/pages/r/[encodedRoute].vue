<template>
  <!-- This template will not render anything, as it's used just for redirection -->
</template>

<script setup>
import { onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";

onMounted(() => {
  const route = useRoute();
  const router = useRouter();
  const base64String = route.params.encodedRoute;

  try {
    // Try decoding the base64 string
    const decodedUrl = atob(base64String);
    // Validate if it's a URL (basic validation)
    const urlPattern =
      /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*(\:[0-9]{1,5})?(\/.*)?$/;

    if (urlPattern.test(decodedUrl)) {
      // If it's a URL, redirect the user to the decoded URL
      window.location.href = decodedUrl;
    } else {
      console.error("The decoded string is not a valid URL:", decodedUrl);
      // Redirect to an error page
      throw new Error("Invalid Page");
    }
  } catch (error) {
    const decodedUrl = atob(base64String);

    console.error("Failed to decode the base64 string:", error);
    // Redirect to an error page
    throw createError({
      statusCode: 404,
      statusMessage: "Inavlid Redirect Page: " + decodedUrl,
      fatal: true,
    });
  }
});
</script>
