<script setup lang="ts">
// Test page for the PaymentModal component
import { ref } from 'vue';

const showPaymentModal = ref(false);
const paymentUrl = ref('');
const currentOrderId = ref('');
const currentOrderAmount = ref(0);
const isLoading = ref(false);

// Test data
const testOrderId = '550e8400-e29b-41d4-a716-446655440000';
const testAmount = 25.50;
const testPaymentUrl = 'https://gateway.dev.keepz.me/ecommerce-service/payment/test';

const openTestModal = () => {
  paymentUrl.value = testPaymentUrl;
  currentOrderId.value = testOrderId;
  currentOrderAmount.value = testAmount;
  showPaymentModal.value = true;
};

const openLoadingModal = () => {
  isLoading.value = true;
  showPaymentModal.value = true;

  // Simulate loading for 3 seconds
  setTimeout(() => {
    isLoading.value = false;
    paymentUrl.value = testPaymentUrl;
    currentOrderId.value = testOrderId;
    currentOrderAmount.value = testAmount;
  }, 3000);
};

const openErrorModal = () => {
  paymentUrl.value = '';
  currentOrderId.value = '';
  currentOrderAmount.value = 0;
  isLoading.value = false;
  showPaymentModal.value = true;
};

const openSuccessModal = () => {
  paymentUrl.value = testPaymentUrl;
  currentOrderId.value = testOrderId;
  currentOrderAmount.value = testAmount;
  isLoading.value = false;
  showPaymentModal.value = true;

  // Simulate payment success after 3 seconds
  setTimeout(() => {
    // Simulate payment success message
    window.postMessage({
      type: 'payment_success',
      status: 'success',
      orderId: testOrderId,
      amount: testAmount
    }, '*');
  }, 3000);
};

const closeModal = () => {
  showPaymentModal.value = false;
  paymentUrl.value = '';
  currentOrderId.value = '';
  currentOrderAmount.value = 0;
  isLoading.value = false;
};

const handlePaymentSuccess = (orderId: string, amount: number) => {
  console.log('Payment success:', orderId, amount);
  alert(`Payment successful! Order ID: ${orderId}, Amount: ${amount} ₾`);
};

const handlePaymentError = (error: string) => {
  console.error('Payment error:', error);
  alert(`Payment error: ${error}`);
};

const { t } = useI18n();
</script>

<template>
  <NuxtLayout name="default">
    <div class="container mx-auto px-4 py-8">
      <h1 class="text-3xl font-bold mb-8 text-center">Payment Modal Test Page</h1>

      <div class="max-w-md mx-auto space-y-4">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold mb-4">Test Payment Modal States</h2>

          <div class="space-y-3">
            <!-- Test Normal Payment Modal -->
            <button
              @click="openTestModal"
              class="w-full py-3 px-4 bg-velvet text-white rounded-lg hover:bg-velvet/90 transition-colors"
            >
              Open Payment Modal (Normal)
            </button>

            <!-- Test Loading State -->
            <button
              @click="openLoadingModal"
              class="w-full py-3 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Open Payment Modal (Loading)
            </button>

            <!-- Test Error State -->
            <button
              @click="openErrorModal"
              class="w-full py-3 px-4 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              Open Payment Modal (Error)
            </button>

            <!-- Test Success State -->
            <button
              @click="openSuccessModal"
              class="w-full py-3 px-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              Open Payment Modal (Success Simulation)
            </button>
          </div>
        </div>

        <div class="bg-gray-50 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-3">Test Data</h3>
          <div class="text-sm space-y-2">
            <p><strong>Order ID:</strong> <code class="bg-gray-200 px-2 py-1 rounded">{{ testOrderId }}</code></p>
            <p><strong>Amount:</strong> <span class="font-mono">{{ testAmount }} ₾</span></p>
            <p><strong>Payment URL:</strong> <code class="bg-gray-200 px-2 py-1 rounded text-xs">{{ testPaymentUrl }}</code></p>
          </div>
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 class="text-lg font-semibold mb-2 text-yellow-800">Instructions</h3>
          <ul class="text-sm text-yellow-700 space-y-1">
            <li>• <strong>Normal:</strong> Shows the payment modal with iframe and buttons</li>
            <li>• <strong>Loading:</strong> Shows loading state for 3 seconds, then normal state</li>
            <li>• <strong>Error:</strong> Shows error state when no payment URL is available</li>
            <li>• <strong>Success Simulation:</strong> Shows payment form, then simulates success after 3 seconds</li>
          </ul>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 class="text-lg font-semibold mb-2 text-blue-800">Features</h3>
          <ul class="text-sm text-blue-700 space-y-1">
            <li>• Embedded iframe for seamless payment experience</li>
            <li>• "Open in New Tab" button for better mobile experience</li>
            <li>• Loading states with spinner animation</li>
            <li>• Error handling with user-friendly messages</li>
            <li>• Fully responsive design (mobile, tablet, desktop)</li>
            <li>• Payment completion detection via iframe messaging</li>
            <li>• Success notifications with automatic modal closure</li>
            <li>• Multi-language support (EN, KA, RU, EL)</li>
            <li>• Adaptive iframe sizing for different screen sizes</li>
            <li>• Success state with animated checkmark</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Payment Modal -->
    <PaymentModal
      :isShowModal="showPaymentModal"
      :paymentUrl="paymentUrl"
      :orderId="currentOrderId"
      :amount="currentOrderAmount"
      :isLoading="isLoading"
      @clickOut="closeModal"
      @close="closeModal"
      @openInNewTab="closeModal"
      @paymentSuccess="handlePaymentSuccess"
      @paymentError="handlePaymentError"
    />
  </NuxtLayout>
</template>

<style scoped>
code {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}
</style>
