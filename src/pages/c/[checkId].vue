<script setup>
const route = useRoute();
const checkId = route.params.checkId;
const encodedID = atob(checkId);

const { getCheck } = await useCheckout();

const { check } = await getCheck(encodedID);
const restaurant = computed(() => check.value.data.restaurant);
const config = useRuntimeConfig();
const loading = useLoading();
onMounted(() => {
  loading.value = false;
});

const { translation: restaurantTranslation } = useTranslations(
  restaurant.value
);
const orders = computed(() => {
  const parsedCheckoutDetails = JSON.parse(check.value.data.order_details);
  //Check if it is an array or not and return accordingly
  if (Array.isArray(parsedCheckoutDetails)) {
    return parsedCheckoutDetails;
  } else if (parsedCheckoutDetails.orders) {
    return parsedCheckoutDetails.orders;
  }
});

const getDate = (date) => {
  const dateObject = new Date("2023-06-11T07:21:44.000000Z");

  const year = dateObject.getFullYear();
  const month = dateObject.getMonth() + 1;
  const day = dateObject.getDate();
  const hour = dateObject.getHours();
  const minute = dateObject.getMinutes();
  const second = dateObject.getSeconds();

  const formattedTime = `${day}/${month}/${year} - ${hour}:${minute}.${second}`;

  return formattedTime;
};

const orderedDishesPrice = computed(() => {
  let result = 0;
  if (orders.value) {
    for (let order of orders.value) {
      order.dish?.forEach((dish) => {
        const effectivePrice =
          dish.pivot && dish.pivot.promo && dish.pivot.promo.newPrice
            ? Number(dish.pivot.promo.newPrice)
            : Number(dish?.price || "0");

        result += effectivePrice * (dish?.quantity || 1);
      });
    }
  }
  return result;
});

const feeAmount = computed(() => {
  let totalFee = 0;
  if (orders.value && restaurant.value?.fee) {
    for (let order of orders.value) {
      let orderPrice = 0;
      order.dish?.forEach((dish) => {
        const effectivePrice =
          dish.pivot && dish.pivot.promo && dish.pivot.promo.newPrice
            ? Number(dish.pivot.promo.newPrice)
            : Number(dish?.price || "0");

        orderPrice += effectivePrice * (dish?.quantity || 1);
      });
      totalFee += (restaurant.value?.fee / 100) * orderPrice;
    }
  }
  return parseFloat(totalFee.toFixed(2));
});

const PaymentSummaries = computed(() => {
  return [
    {
      title: "subTotal",
      price: orderedDishesPrice.value.toFixed(2),
    },

    {
      title: "serviceFee",
      price: feeAmount.value.toFixed(2),
    },
    {
      title: "discount",
      price: check.value.data.discount.toFixed(2),
    },
    {
      title: "tip",
      price: check.value.data.tip.toFixed(2),
    },
    {
      title: "total",
      price: (
        orderedDishesPrice.value +
        feeAmount.value +
        check.value.data.tip -
        check.value.data.discount
      ).toFixed(2),
    },
    {
      title: "totalPaid",
      price:
        check.value.data.method == "cash" && check.value.data.change
          ? (
              Number(check.value.data.change) +
              Number(check.value.data.amount) +
              Number(check.value.data.tip)
            ).toFixed(2)
          : (
              orderedDishesPrice.value +
              feeAmount.value +
              check.value.data.tip
            ).toFixed(2),
    },
    {
      title: "change",
      price: check.value.data.change
        ? Number(check.value.data.change).toFixed(2)
        : "0.00",
    },
  ];
});

const shareUrl = function () {
  if (!navigator.share) return;

  navigator
    .share({
      url: `/c/${checkId}`,
      title: "Sharing check",
    })
    .then(() => {})
    .catch((error) => {});
};
</script>

<template>
  <NuxtLayout name="table-layout">
    <section>
      <time class="text-cocoa font-medium">
        {{ getDate(check.data.created_at) }}
      </time>
      <p class="text-cocoa font-bold">
        {{ restaurantTranslation.title }}
      </p>

      <OrderedItems :orders="orders" />
      <ul class="mt-10">
        <PaymentSummary
          v-for="summary in PaymentSummaries"
          :key="summary.price"
          :title="summary.title"
          :price="summary.price"
        />
      </ul>
      <button
        @click="shareUrl"
        class="w-full mt-5 py-4 bg-white border focus:bg-white hover:bg-white"
      >
        <span class="text-cocoa font-bold">{{ $t("shareCheck") }}</span>
      </button>
    </section>
  </NuxtLayout>
</template>

<style scoped></style>
