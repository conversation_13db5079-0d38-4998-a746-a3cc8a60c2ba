import { Checkout } from "./commonTypes";

export const AccountStatus = {
  ACTIVE: "active",
  SUSPENDED: "suspended",
  DELETED: "deleted",
} as const;

export interface User {
  id: number;
  first_name: string;
  last_name: string;
  email?: string;
  password?: string; // Optional since it can be null if using OAuth
  phone?: string; // Assuming phone is optional
  account_status: (typeof AccountStatus)[keyof typeof AccountStatus];
  external_user_id?: string; // Optional: UserID from OAuth provider
  provider_id?: string; // Optional: ID from OAuth provider
  provider_name?: string; // Optional: Name of the OAuth provider (e.g., 'google', 'facebook', 'instagram')
  oauth_token?: string; // Optional: OAuth token if needed for further API calls
  refresh_token?: string; // Optional: For OAuth refresh token
  profile_picture_url?: string; // Optional: URL to the user's profile picture from OAuth provider
  email_verified_at?: string; // Optional: For email verification timestamp
  last_login_at?: string; // Optional: For last login timestamp
  created_at: string; // Creation timestamp
  updated_at: string; // Last update timestamp
  token?: string; // For Front-end
  history?: Checkout[]; // Optional: Array of Checkout objects (user's checkout history)
  isPasswordSet?: Boolean; // Optional: For checking if user has set a password
}

export interface UserUpdateRequest {
  first_name?: string;
  last_name?: string;
  email?: string;
  password?: string;
  phone?: string;
  current_password?: string;
}

export interface LoginResponse {
  message: string;
  status: boolean;
  data: {
    token: string;
    user: User;
  };
}

export interface LogoutResponse {
  message: string;
  status: boolean;
  data: {};
}

export type UserWithoutPassword = Omit<User, "password">;

export const USER_COOKIE = "mnu-user";
export const OAUTH_TOKEN_COOKIE = "sb-mhb-access-token";
export const OAUTH_REFRESH_TOKEN_COOKIE = "sb-mhb-access-token";
