import {
  Restaurant,
  Category,
  Ingredient,
  GalleryItem,
  Promo,
  Rating,
} from "./commonTypes";

export interface TableData {
  data?: Table;
}

export interface Table {
  id?: number;
  title?: string;
  restaurant?: Restaurant;
  items?: Item[];
  gallery?: GalleryItem[];
}

export interface Item {
  sound: string;
  id?: number;
  restaurant_id?: number;
  price?: string;
  timer?: string;
  image?: string;
  status?: number;
  title?: string;
  category?: Category[];
  ingredient?: Ingredient[];
  translations?: ItemTranslation[];
  audio?: string;
  ratings?: Rating[];
  promos?: Promo[];
}

export interface ItemTranslation {
  id?: number;
  locale?: string;
  item_id?: number;
  title?: string;
}
