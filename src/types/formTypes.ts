// Define  interfaces for your form data
export interface ProfileForm {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: any;
}

export interface LoginForm {
  email: string;
  password: string;
}

export interface ChangePasswordForm {
  currentPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}

export interface RegisterForm {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phoneNumber: any;
  agreeToTermsAndConditions: boolean;
}

export interface RegisterFormErrors {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phoneNumber: string;
  agreeToTermsAndConditions: string;
}

export interface LoginFormErrors {
  email: string;
  password: string;
}

export interface ChangePasswordFormErrors {
  currentPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}
export interface ProfileFormErrors {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
}
