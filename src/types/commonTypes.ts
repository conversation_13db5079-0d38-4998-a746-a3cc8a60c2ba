import { User } from "./authTypes";

export interface Currency {
  id?: number;
  name: string;
  shortName: string;
  symbol: string;
  created_at?: string;
  updated_at?: string;
}

export interface Discount {
  id?: number;
  code: string;
  amount: number; // If amount should be a number, change to 'number'
  expires_at?: string;
  status: boolean; // If status is a boolean or number, adjust the type accordingly
  description: string;
  uuid: string;
  restaurant_id: number;
  redeems?: number;
  updated_at?: string;
  created_at?: string;
}

export interface Restaurant {
  uuid?: string;
  title?: string;
  slug?: string;
  address?: string;
  admin_phone?: string;
  open_from?: string;
  open_to?: string;
  created_at?: string;
  updated_at?: string;
  translations?: RestaurantTranslation[];
  fee?: number;
  logo?: string;
  cover?: string;
  currency?: Currency;
  delayed?: Boolean;
  paymentMethods?: PaymentMethodRel[];
}

export interface PaymentMethodRel {
  restaurant_id: number;
  method_id: number;
  active: boolean;
  created_at: string;
  updated_at: string;
  paymentMethodData?: PaymentMethod;
  restaurant?: Restaurant;
}

export interface PaymentMethod {
  id: number;
  method_code: string;
  method_name: string;
  created_at: string;
  updated_at: string;
  paymentMethods?: PaymentMethodRel[];
}

export interface RestaurantTranslation {
  id?: number;
  locale?: string;
  restaurant_id?: number;
  title?: string;
  address?: string;
}

export interface Category {
  id: number;
  restaurant_id?: number;
  title?: string;
  image?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  translations?: CategoryTranslation[];
  order_by?: number;
}

export interface CategoryTranslation {
  id?: number;
  locale?: string;
  category_id?: number;
  title?: string;
}

export interface CategoryList {
  name: string;
  count: number;
  data: Category;
}

export interface Ingredient {
  id?: number;
  title?: string;
  restaurant_id?: number;
  stock?: string;
  unit?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
  translations: IngredientTranslation[];
}

export interface IngredientTranslation {
  id?: number;
  locale?: string;
  ingredient_id?: number;
  title?: string;
}

export interface Pivot {
  cart_id?: number;
  dish_id?: number;
  quantity?: number;
  promo?: Promo;
}

export interface Dish {
  id?: number;
  restaurant_id?: number;
  title?: string;
  price?: string;
  timer?: string;
  image?: string;
  status?: number;
  created_at?: string;
  updated_at?: string;
  pivot?: Pivot;
  category?: Category[];
  ingredient?: Ingredient[];
  translations?: DishTranslation[];
  quantity?: number;
  promos?: Promo[];
}

export interface DishTranslation {
  id?: number;
  locale?: string;
  dish_id?: number;
  title?: string;
}

export interface GalleryItem {
  id?: number;
  title: string; // Title of the gallery item
  titlePosition: "top" | "bottom"; // Position of the title, either at the top or bottom
  image: string; // URL of the image
  status?: number; // Visibility status of the item
  placement: number; // Position in the carousel
  restaurant_id: number;
  translations?: GalleryItemTranslation[];
  restaurant?: Restaurant;
  created_at?: string;
  updated_at?: string;
}

export interface GalleryItemTranslation {
  id?: number;
  galleryItemId: number;
  title: string;
  locale: string;
}
export interface Promo {
  id?: number;
  restaurant_id: number;
  dish_id: number;
  start: string;
  end: string;
  newPrice: number;
  status?: number;
  created_at?: string;
  updated_at?: string;
  title: string; // This seems to be redundant since translations are available
  description: string; // This seems to be redundant since translations are available
  dish?: Dish; // Assuming you have a Dish interface defined elsewhere
  translations?: PromoTranslation[];
}

export interface PromoTranslation {
  id?: number;
  locale: string;
  promo_id: number;
  title: string;
  description: string;
  created_at?: string;
  updated_at?: string;
}

export interface Rating {
  id?: number;
  restaurant_id: number;
  dish_id: number;
  table_id: number;
  rate: number;
  review?: string;
  created_at?: string;
  updated_at?: string;
}
export interface RatingData {
  status?: boolean;
  message?: string;
  data?: Rating;
}

export interface DiscountData {
  status?: boolean;
  message?: string;
  data?: Discount;
}
export interface Checkout {
  id: number;
  restaurant_id: number;
  space_id?: number;
  table_id?: number;
  client_id: number;
  quantity: number;
  amount: number;
  discount: number;
  change: number;
  discount_code: string;
  method?: string;
  tip: number;
  order_details: string;
  created_at: string;
  updated_at: string;
  client?: User;
  restaurant_name?: string;
  restaurant_logo?: string;
  client_ids?: string;
}
