<!-- <!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Loader</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css"
    />
    <style>
      .loader-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
      }
      .text-black {
        color: black;
      }
    </style>
  </head>
  <body>
    <div class="loader-container">
      <div class="text-center text-black">
        <i class="fa fa-spin fa-spinner fa-5x"></i>
      </div>
    </div>
  </body>
</html> -->

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Loader</title>
    <!-- Import Tailwind CSS from CDN -->
    <link
      href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
      rel="stylesheet"
    />
    <style>
      .shimmer {
        animation: shimmer 2s infinite linear;
        background: linear-gradient(
          to right,
          #f0f0f0 8%,
          #e0e0e0 18%,
          #f0f0f0 33%
        );
        background-size: 800px 104px;
        position: relative;
      }

      @keyframes shimmer {
        0% {
          background-position: -100% 0;
        }
        100% {
          background-position: 100% 0;
        }
      }
    </style>
  </head>
  <body>
    <div class="space-y-6 h-[100vh] m-16">
      <!-- Carousel Skeleton -->
      <div class="h-36 bg-gray-200 rounded-xl shimmer"></div>

      <!-- DishSearch Skeleton -->
      <div class="h-12 rounded-full bg-gray-200 shimmer"></div>

      <!-- CategoryCard Skeletons -->
      <div class="grid grid-cols-2 gap-4">
        <div class="h-32 w-[100%] bg-gray-200 rounded-xl shimmer"></div>
        <div class="h-32 w-[100%] bg-gray-200 rounded-xl shimmer"></div>
        <div class="h-32 w-[100%] bg-gray-200 rounded-xl shimmer"></div>
        <div class="h-32 w-[100%] bg-gray-200 rounded-xl shimmer"></div>
      </div>
    </div>
  </body>
</html>
