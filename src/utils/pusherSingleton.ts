import Pusher from 'pusher-js';

class PusherSingleton {
  private static instance: Pusher | null = null;
  private static channels: { [key: string]: any } = {};

  private constructor() {}

  static getInstance(config: { 
    key: string; 
    cluster: string;
    wsHost?: string;
    wsPort?: number;
    wssPort?: number;
    forceTLS?: boolean;
    enabledTransports?: string[];
  }): Pusher | null {
    if (!process.client) {
      return null;
    }

    if (!this.instance) {
      try {
        this.instance = new Pusher(config.key, {
          cluster: config.cluster,
          wsHost: config.wsHost,
          wsPort: config.wsPort,
          wssPort: config.wssPort,
          forceTLS: config.forceTLS ?? true,
          enabledTransports: config.enabledTransports ?? ['ws', 'wss'],
        });
      } catch (error) {
        return null;
      }
    }

    return this.instance;
  }

  static getChannel(channelName: string): any {
    if (!this.instance) {
      return null;
    }

    if (!this.channels[channelName]) {
      this.channels[channelName] = this.instance.subscribe(channelName);
    }

    return this.channels[channelName];
  }

  static unsubscribe(channelName: string): void {
    if (this.channels[channelName]) {
      this.instance?.unsubscribe(channelName);
      delete this.channels[channelName];
    }
  }

  static disconnect(): void {
    if (this.instance) {
      this.instance.disconnect();
      this.instance = null;
      this.channels = {};
    }
  }
}

export default PusherSingleton;
