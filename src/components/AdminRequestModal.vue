<script setup lang="ts">
const { closeModal, modals } = useModal();

const { adminAprove } = useAdmin();

const isModalOpen = computed((): boolean => {
  const modal = modals.value.find((modal) => modal.id === "adminRequest");
  if (modal) {
    return modal.visible;
  }
  return false;
});
</script>
<template>
  <BaseModal :modalActive="isModalOpen" @clickOut="closeModal('adminRequest')">
    <div class="flex flex-col items-center gap-3">
      <h3 class="text-xl font-semibold text-cocoa">{{ $t("adminRequest") }}</h3>
      <p class="text-center text-base text-cocoa font-semibold">
        {{ $t("customerWantsToEdit") }}
      </p>
      <Button
        @click="adminAprove"
        class="w-full mt-6 py-4 bg-white border border-velvet rounded-xl focus:bg-white focus:ring-white focus:bg-white hover:bg-white"
      >
        <span class="text-cocoa font-bold"> {{ $t("accept") }} </span>
      </Button>
    </div>
    <Button
      @click="closeModal('adminRequest')"
      class="w-full mt-2 py-4 bg-white border-velvet border border-velvet rounded-xl focus:bg-white focus:ring-white focus:bg-white hover:bg-white"
    >
      <span class="text-cocoa font-bold"> {{ $t("deny") }} </span>
    </Button>
  </BaseModal>
</template>

<style scoped></style>
