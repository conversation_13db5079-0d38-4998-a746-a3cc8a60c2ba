<script setup lang="ts">
import { CategoryList } from "types/commonTypes";

const emit = defineEmits<{
  (e: "onCategorySelect", category: CategoryList): void;
}>();

type PropTypes = {
  categories: CategoryList[] | null;
  selected: CategoryList;
};

const props = defineProps<PropTypes>();

const selectCategory = (category: CategoryList) => {
  if (
    props.selected &&
    props.selected.data &&
    category.data.id === props.selected.data.id
  ) {
    emit("onCategorySelect", {} as CategoryList);
    return;
  }
  emit("onCategorySelect", category);
};

const isSelected = (category: CategoryList) => {
  if (
    props.selected &&
    props.selected.data &&
    category.data.id === props.selected.data.id
  ) {
    return true;
  }
  return false;
};
</script>

<template>
  <ul class="overflow-x-auto overflow-y-hidden flex items-center gap-3 h-16">
    <li
      class="border-secondary cursor-pointer tracking-wide rounded-lg whitespace-nowrap text-cocoa text-base font-semibold pt-1 pb-2 f"
      v-for="category in categories"
      :key="category.data.id"
      @click="selectCategory(category)"
      :class="{
        'rounded pt-1 text-white pr-3 pl-3 bg-velvet': isSelected(category),
      }"
    >
      {{ category.name }}
    </li>
  </ul>
</template>

<style scoped>
span {
  color: grey;
  cursor: pointer;
  font-weight: 600;
  white-space: nowrap;
}

span:hover {
  opacity: 0.6;
}
</style>
