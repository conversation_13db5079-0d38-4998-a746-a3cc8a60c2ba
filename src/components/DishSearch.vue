<script setup>
import { Input } from "flowbite-vue";
defineProps(["modelValue"]);
defineEmits(["update:modelValue"]);
</script>
<template>
  <Input
    class="caret-red-700 rounded-2xl py-4 text-gray-400 font-medium placeholder-gray-300 focus:border-velvet focus:outline-none focus:ring-velvet"
    :placeholder="$t('searchDish')"
    :value="modelValue"
    @input="$emit('update:modelValue', $event.target.value)"
  >
    <template #suffix>
      <Icon class="mb-1.5" name="tabler:search" size="20" color="#D9D9D9" />
    </template>
  </Input>
</template>

<style scoped></style>
