<template>
  <div class="flex items-center my-16">
    <div class="flex-grow h-0.5 bg-gray-300"></div>
    <span class="px-4 bg-white relative z-10 text-4xl font-bold" id="subhead">{{
      text
    }}</span>
    <div class="flex-grow h-0.5 bg-gray-300"></div>
  </div>
</template>

<script>
export default {
  props: {
    text: {
      type: String,
      required: true,
    },
    lineLength: {
      type: String,
      default: "50%", // You can adjust this default value or pass a prop when using the component
    },
  },
};
</script>

<style scoped>
/* Optionally, add any additional styling here */
</style>
