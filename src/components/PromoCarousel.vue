<template>
  <div
    v-if="expandedImage.isExpanded"
    class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-10"
    @click="expandedImage.isExpanded = false"
  >
    <NuxtImg
      :src="expandedImage.url"
      class="max-w-full max-h-full rounded-lg transition-transform duration-500"
    />
  </div>

  <div class="relative w-full">
    <!-- Slider Wrapper -->

    <div class="overflow-hidden">
      <div
        class="flex transition-transform duration-300"
        :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
      >
        <!-- Slides -->
        <div
          v-for="(item, index) in items"
          :key="index"
          class="flex-shrink-0 w-full relative"
        >
          <!-- Image -->
          <NuxtImg
            :src="item.image ? item.image : config.public.defaultImage"
            class="w-full md:h-[50vh] sm:h-[35vh] h-[30vh] object-cover"
            :alt="`${takeTranslation(item).title} Promo`"
            @click="expandImage(item.image)"
          />

          <!-- Combined Title and Badge -->
          <div
            class="absolute top-4 left-1/2 transform w-full -translate-x-1/2 text-white backdrop-blur-xs bg-black/20 px-4 py-2 rounded-2xl flex items-center justify-center"
          >
            <div class="flex flex-row items-center w-full justify-between">
              <h3
                class="text-xl md:text-3xl font-semibold"
                v-if="item.promos && item.promos.length > 0 && item.promos[0]"
              >
                {{ takeTranslation(item.promos[0]).title }}
              </h3>

              <!-- Badge -->
              <div class="ml-4 flex justify-center items-center">
                <div class="relative">
                  <div
                    class="absolute z-10 sm:top-4 top-2 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white border border-red-500 rounded-full"
                  ></div>
                  <div
                    class="bg-red-500 text-white rounded-full w-20 h-20 sm:w-28 sm:h-28 p-4 py-4 sm:py-6 relative text-center"
                  >
                    <div
                      class="absolute inset-0 m-1 border-2 border-yellow-200 border-dotted rounded-full"
                    ></div>
                    <div class="text-xs sm:text-base line-through">
                      {{ item.price }}
                      {{
                        restaurant && restaurant.currency
                          ? restaurant.currency.symbol
                          : "GEL"
                      }}
                    </div>
                    <div
                      class="text-md sm:text-xl font-bold"
                      v-if="
                        item.promos &&
                        item.promos.length > 0 &&
                        item.promos[0] &&
                        'newPrice' in item.promos[0]
                      "
                    >
                      {{ item.promos[0].newPrice }}
                      {{
                        restaurant && restaurant.currency
                          ? restaurant.currency.symbol
                          : "GEL"
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="absolute bottom-0 mt-4 right-1 px-0 p-2">
            <div class="text-black mt-4 w-32">
              <ItemAdd :item="item" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Buttons -->
    <button
      class="absolute top-1/2 left-0 transform -translate-y-1/2 bg-black bg-opacity-30 text-white text-2xl font-bold p-3 rounded-full shadow-md"
      @click="prevSlide"
    >
      &lt;
    </button>
    <button
      class="absolute top-1/2 right-0 transform -translate-y-1/2 bg-black bg-opacity-30 text-white text-2xl font-bold p-3 rounded-full shadow-md"
      @click="nextSlide"
    >
      &gt;
    </button>

    <!-- Navigation Dots -->
    <div class="flex justify-center space-x-2 p-2">
      <span
        v-for="n in items.length"
        :key="n"
        @click="goToSlide(n - 1)"
        class="block w-2 h-2 bg-gray-400 rounded-full cursor-pointer"
        :class="{ 'bg-blue-500': n - 1 === currentSlide }"
      ></span>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  autoplayInterval: {
    type: Number,
    default: 3000, // default to 3 seconds
  },
});

const config = useRuntimeConfig();
const expandedImage = reactive({
  url: "",
  isExpanded: false,
});

const expandImage = (imageUrl) => {
  expandedImage.url = imageUrl;
  expandedImage.isExpanded = true;
};

const { takeTranslation } = useTranslation();
const { restaurant } = useTable();

const currentSlide = ref(0);
let autoplayTimer = null;
const nextSlide = () => {
  if (currentSlide.value < props.items.length - 1) {
    currentSlide.value++;
  } else {
    currentSlide.value = 0;
  }
};

const prevSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--;
  } else {
    currentSlide.value = props.items.length - 1;
  }
};
const goToSlide = (index) => {
  currentSlide.value = index;
};

const startAutoplay = () => {
  autoplayTimer = setInterval(() => {
    nextSlide();
  }, props.autoplayInterval);
};

const stopAutoplay = () => {
  clearInterval(autoplayTimer);
};

onMounted(startAutoplay);
onUnmounted(stopAutoplay);
</script>

<style scoped>
.line-through {
  text-decoration: line-through;
}
</style>
