<script setup lang="ts">
import { Item } from "types/table-types";
type PropTypes = {
  item: Item | undefined;
};
import { Input } from "flowbite-vue";
import Button from "./Button.vue";
const isAdmin = useState("isAdmin");

const props = defineProps<PropTypes>();
const { cartDishes } = useCart();

const cartDish = computed(() => {
  if (item?.id && cartDishes.value) {
    return cartDishes.value.find((dish) => dish?.id === item?.id);
  }
});

const { item } = props;
const { addDish, decreaseQuantity, increaseQuantity, pending } = useCart();

const capitalizeFirstLetter = (str: string) => {
  if (!str || typeof str !== "string") {
    throw new Error("The input must be a string");
  }
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};
</script>

<template>
  <div>
    <article>
      <Button
        v-if="!cartDish"
        class="flex justify-center items-center font-bold bg-velvet rounded-xl w-[100%] px-12"
        :disabled="pending"
        @click="addDish(item?.id)"
      >
        <span
          v-if="!pending"
          class="font-semibold text-[0.75rem] tracking-wider"
        >
          {{ capitalizeFirstLetter($t("addToCart")) }}
        </span>
        <div
          v-else
          class="w-8 border-none text-center flex items-center justify-center"
        >
          <Icon name="svg-spinners:180-ring" />
        </div>
      </Button>
      <div v-else class="flex">
        <Button
          @click="decreaseQuantity(item?.id)"
          :disabled="pending"
          class="bg-velvet w-10 h-10 flex justify-center items-center relative left-2 rounded-xl disabled:opacity-100"
        >
          <Icon name="ic:baseline-minus" class="text-lg" />
        </Button>

        <input
          v-if="!pending"
          type="text"
          :value="cartDish.quantity"
          readonly="true"
          class="w-16 border-none text-center bg-gray-100 focus:outline-none focus:ring-0 focus:ring-blue-300"
        />
        <div
          v-else
          class="w-16 border-none text-center bg-gray-100 flex items-center justify-center"
        >
          <Icon name="svg-spinners:180-ring" />
        </div>
        <Button
          @click="increaseQuantity(item?.id)"
          class="bg-velvet w-10 h-10 flex justify-center items-center relative right-2 rounded-xl disabled:opacity-100 outline-red-700"
          :disabled="pending"
        >
          <Icon name="ic:outline-plus" class="text-lg" />
        </Button>
      </div>
    </article>
  </div>
</template>
