<script setup>
const props = defineProps({
  sound: String,
});

const isPlaying = ref(false);

const playSound = () => {
  try {
    if (isPlaying.value) {
      return;
    }
    isPlaying.value = true;
    const sound = new Audio(props.sound ? props.sound : "/khinkali.mp3");

    sound.play().catch((error) => {
      console.error("Error playing sound:", error);
      // Fallback to default sound if there was an error playing the provided sound
      if (sound.src !== "/khinkali.mp3") {
        sound.src = "/khinkali.mp3";
        sound.play();
      }
    });

    sound.onended = () => {
      isPlaying.value = false;
    };
  } catch (error) {
    isPlaying.value = false;
  }
};
</script>

<template>
  <i
    v-if="!isPlaying"
    style="font-size: 18px"
    class="icofont-play-alt-1"
    @click="playSound"
  ></i>
  <i v-else style="font-size: 18px" class="icofont-stop"></i>
</template>

<style scoped>
i {
  cursor: pointer;
}
</style>
