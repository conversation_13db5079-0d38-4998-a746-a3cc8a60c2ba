<script setup>
import BaseModal from "./BaseModal.client.vue";
import Button from "./Button.vue";
defineProps({
  isShowModal: {
    default: false,
  },
});
defineEmits(["onReceiptClick"]);
</script>
<template>
  <BaseModal :modalActive="isShowModal" @clickOut="$emit('clickOut')">
    <main
      class="px-10 flex flex-col gap-10 font-semibold text-cocoa justify-center items-center text-center"
    >
      <p>{{ $t("requestReceived") }}</p>
      <div @click="$emit('onReceiptClick')">
        <Button class="bg-velvet">{{ $t("receiptCheck") }}</Button>
      </div>
      <p>{{ $t("waitForBillCheck") }}</p>
    </main>
  </BaseModal>
</template>
