<template>
  <div class="relative">
    <label :class="error ? 'text-red-500' : 'text-gray-700'"
      >{{ $t(label)
      }}<sup v-if="required" class="text-xs ml-[0.2rem]">*</sup></label
    >
    <Input
      :type="type"
      v-model="localValue"
      @update:modelValue="updateValue"
      class="caret-red-700 mb-2 rounded-2xl py-4 text-gray-400 font-medium placeholder-gray-300 focus:border-velvet focus:outline-none focus:ring-velvet w-full"
      :placeholder="t(label)"
      :class="error ? 'border-red-500' : ''"
      ><template #prefix v-if="isIcon">
        <slot name="icon-area" />
      </template>
    </Input>
    <div v-if="error" class="absolute bottom-0 text-sm mt-0 text-red-500">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { Input } from "flowbite-vue";

const { t } = useI18n();

type InputType =
  | "number"
  | "text"
  | "button"
  | "time"
  | "image"
  | "hidden"
  | "color"
  | "search"
  | "checkbox"
  | "date"
  | "datetime-local"
  | "email"
  | "file"
  | "month"
  | "password"
  | "radio"
  | "range"
  | "reset"
  | "submit"
  | "tel"
  | "url"
  | "week";
const emits = defineEmits(["update:modelValue"]);
// Define props
const props = defineProps({
  label: {
    type: String,
    required: true,
  },
  modelValue: {
    type: String,
    required: true,
  },
  type: {
    type: String as () => InputType,
    default: "text",
  },
  error: {
    type: String,
    default: "",
  },
  isIcon: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
});

// Destructure and create reactive references
const { label, modelValue, type, error, isIcon, required } = toRefs(props);

// Create a local copy of the modelValue
const localValue = ref(modelValue.value);

// Update localValue when modelValue changes (prop to local)
watch(modelValue, (newValue) => {
  localValue.value = newValue;
});

// Emit update:modelValue event when localValue changes (local to parent)
const updateValue = (value) => {
  emits("update:modelValue", value);
};
</script>
