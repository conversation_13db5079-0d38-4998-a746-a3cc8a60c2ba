<script setup lang="ts">
type PropTypes = {
  title: string;
  price: string;
};

const props = defineProps<PropTypes>();
const { restaurant } = useTable();
</script>

<template>
  <li class="border-b flex justify-between pb-2 font-bold text-cocoa mt-2">
    <div>{{ $t(title) }}</div>
    <div>
      {{ price }}
      {{
        restaurant && restaurant.currency ? restaurant.currency.symbol : "GEL"
      }}
    </div>
  </li>
</template>
