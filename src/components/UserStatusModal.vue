<script setup lang="ts">
const { closeModal, modals } = useModal();
const { adminRequest, pending } = useAdmin();

const sendAdminRequest = async () => {
  await adminRequest();
  closeModal("userStatus");
};

const isModalOpen = computed((): boolean => {
  const modal = modals.value.find((modal) => modal.id === "userStatus");
  if (modal) {
    return modal.visible;
  }
  return false;
});
</script>
<template>
  <BaseModal :modalActive="isModalOpen" @clickOut="closeModal('userStatus')">
    <div class="flex flex-col items-center gap-3">
      <Button class="w-14 h-14 flex justify-center items-center bg-velvet">
        <span class="text-5xl font-bold">G</span>
      </Button>
      <h3 class="text-xl font-semibold text-cocoa">{{ $t("guest") }}</h3>
      <p class="text-center text-base text-cocoa font-semibold">
        {{ $t("useMenuCantOrder") }}
      </p>
      <Button
        :loading="pending"
        @click="sendAdminRequest"
        class="w-full mt-6 py-4 h-14 bg-white border border-velvet rounded-xl focus:bg-white focus:ring-white focus:bg-white hover:bg-white flex justify-center"
      >
        <span v-if="!pending" class="text-cocoa font-bold">
          {{ $t("adminRequest") }}
        </span>
      </Button>
    </div>
    <Button
      @click="closeModal('userStatus')"
      class="w-full mt-2 py-4 h-14 bg-white border-velvet border border-velvet rounded-xl focus:bg-white focus:ring-white ocus:bg-white hover:bg-white"
    >
      <span class="text-cocoa font-bold">{{ $t("continueAsGuest") }} </span>
    </Button>
  </BaseModal>
</template>

<style scoped></style>
