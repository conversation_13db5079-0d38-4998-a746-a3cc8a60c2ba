<template>
  <div class="space-y-6">
    <!-- Carousel Skeleton -->
    <div class="h-[20vh] bg-gray-200 rounded-xl shimmer"></div>

    <!-- DishSearch Skeleton -->
    <div class="h-12 rounded-full bg-gray-200 shimmer"></div>

    <!-- CategoryCard Skeletons -->
    <div class="grid grid-cols-2 gap-4">
      <div
        v-for="n in 4"
        :key="n"
        class="h-32 w-[100%] bg-gray-200 rounded-xl shimmer"
      ></div>
    </div>

    <!-- ItemCards Skeletons -->
    <!-- <div
      v-for="m in 2"
      :key="m"
      class="grid grid-cols-3 gap-x-8 py-5 animate-pulse"
    >
      <div class="col-span-1 h-28 w-28 bg-gray-200 rounded-2xl shimmer"></div>

      <div class="col-span-2 flex flex-col justify-between pt-1">
        <div class="flex justify-between">
          <div class="h-4 w-1/4 bg-gray-200 shimmer"></div>
          <div class="h-4 w-4 bg-gray-200 rounded shimmer"></div>
        </div>
        <div class="mt-2 h-4 w-1/4 bg-gray-200 rounded shimmer"></div>
        <div class="flex items-center justify-between h-10">
          <div class="h-4 w-1/4 bg-gray-200 rounded shimmer"></div>
          <div class="h-10 w-32 bg-gray-200 rounded-full shimmer"></div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<style scoped>
.shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(to right, #f0f0f0 8%, #e0e0e0 18%, #f0f0f0 33%);
  background-size: 800px 104px;
  position: relative;
}

@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}
</style>
