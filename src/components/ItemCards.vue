<script setup lang="ts">
import { Alert } from "flowbite-vue";
import { Dish } from "types/commonTypes";
import { Item } from "types/table-types";
type PropTypes = {
  items: Item[] | null;
  alert?: string;
  showImage?: boolean;
};
const config = useRuntimeConfig();
const props = withDefaults(defineProps<PropTypes>(), {
  showImage: true,
});
const { takeTranslation } = useTranslation();
const { restaurant } = useTable();

// Define a type for the state
interface State {
  itemRefs: { [key: number]: HTMLElement };
  expandedImage: {
    url: string;
    isExpanded: boolean;
  };
  currentItem: Dish | null;
}

const toggleImageExpansion = (item: Dish | null) => {
  if (item) {
    state.expandedImage.url = item.image as string;
    state.expandedImage.isExpanded = !state.expandedImage.isExpanded;
    state.currentItem = item; // Store the current item
  } else {
    state.expandedImage.url = "";
    state.expandedImage.isExpanded = !state.expandedImage.isExpanded;
    state.currentItem = item; // Store the current item
  }
};

const state: State = reactive({
  itemRefs: {},
  currentItem: null, // Add this line
  expandedImage: {
    url: "",
    isExpanded: false,
  },
});

const animateCardToCart = (element: any) => {
  const clonedElement = element.cloneNode(true) as HTMLElement;
  document.body.appendChild(clonedElement);

  const cartButton = document.querySelector("#cart-button"); // Assuming your cart button has an id of 'cart-button'
  const { left: cartLeft, top: cartTop } = cartButton!.getBoundingClientRect();
  const { left: cardLeft, top: cardTop } = element.getBoundingClientRect();

  clonedElement.style.position = "fixed";
  clonedElement.style.left = `${cardLeft}px`;
  clonedElement.style.top = `${cardTop}px`;
  clonedElement.style.transition = "all 2.5s ease-in-out";
  clonedElement.style.transformOrigin = "bottom right";

  requestAnimationFrame(() => {
    clonedElement.style.transform = `translate(${cartLeft - cardLeft}px, ${
      cartTop - cardTop
    }px) scale(0.1)`;
    clonedElement.style.opacity = "0";
  });

  clonedElement.addEventListener("transitionend", () => {
    document.body.removeChild(clonedElement);
  });
};
</script>
<style scoped>
img.transition-transform {
  transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
}
.expanded {
  transform: scale(1.2); /* Adjust scale as needed */
  opacity: 1;
}
</style>

<template>
  <div
    v-if="state.expandedImage.isExpanded"
    class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-10"
    @click="toggleImageExpansion(null)"
  >
    <NuxtImg
      :src="
        state.expandedImage.url
          ? state.expandedImage.url
          : config.public.defaultImage
      "
      class="max-w-full max-h-full rounded-lg transition-transform duration-500"
      :class="{ expanded: state.expandedImage.isExpanded }"
    />
    <!-- <div
      class="absolute top-[75vh] left-1/2 transform -translate-x-1/2 bg-black -translate-y-1/2 w-[75%] bg-opacity-30 px-4 py-2 text-xs text-white rounded-xl text-center"
    >
      {{ takeTranslation(state.currentItem).title }}
    </div> -->
  </div>
  <article
    v-for="(item, index) in items"
    :key="item.id"
    :class="[
      { 'border-b': index + 1 !== items?.length },
      { 'grid-cols-3': showImage },
    ]"
    class="grid grid-cols-2 gap-x-2 py-5"
    :ref="el => { if (el) state.itemRefs[index] = el as HTMLElement; }"
  >
    <section class="relative" v-if="showImage">
      <NuxtImg
        class="w-28 h-28 aspect-square cursor-pointer object-cover rounded-2xl"
        :src="item.image ? item.image : config.public.defaultImage"
        @click="toggleImageExpansion(item)"
      />
      <div
        class="absolute left-0 top-0 bg-velvet px-3 py-1 font-semibold rounded-2xl text-white"
        v-if="!restaurant?.delayed"
      >
        {{ item.timer + $t("mins") }}
      </div>
    </section>
    <section class="col-span-2 flex flex-column flex-col justify-between pt-1">
      <div class="flex justify-between">
        <h3 class="text-cocoa font-semibold tracking-wider">
          {{ takeTranslation(item).title }}
        </h3>
        <ItemSound :sound="takeTranslation(item).audio" />
      </div>
      <ItemIgredients :ingredients="item.ingredient" />
      <div class="flex items-center justify-between h-14 sm:h-10">
        <div class="flex flex-col sm:items-center sm:flex-row sm:space-x-2">
          <div class="flex flex-col sm:flex-row sm:space-x-2">
            <span
              v-if="
                item.promos &&
                item.promos.length > 0 &&
                item.promos[0] &&
                'newPrice' in item.promos[0]
              "
              class="line-through text-gray-400"
            >
              {{ item.price }}
              {{
                restaurant && restaurant.currency
                  ? restaurant.currency.symbol
                  : "GEL"
              }}
            </span>
            <!-- Display new price if promo exists, otherwise display regular price -->
            <span
              class="text-cocoa font-bold"
              :class="{
                'text-red-500':
                  item.promos &&
                  item.promos.length > 0 &&
                  item.promos[0] &&
                  'newPrice' in item.promos[0],
              }"
            >
              {{
                item.promos &&
                item.promos.length > 0 &&
                item.promos[0] &&
                "newPrice" in item.promos[0]
                  ? item.promos[0].newPrice
                  : item.price
              }}
              {{
                restaurant && restaurant.currency
                  ? restaurant.currency.symbol
                  : "GEL"
              }}
            </span>
          </div>
          <!-- Promo badge -->
          <span
            v-if="
              item.promos &&
              item.promos.length > 0 &&
              item.promos[0] &&
              'newPrice' in item.promos[0]
            "
            class="bg-red-500 text-white px-2 py-1 h-6 rounded-full text-xs sm:block text-center hidden"
          >
            {{ takeTranslation(item.promos[0]).title }}
          </span>
          <ItemRating :item="item" v-if="item.ratings" />
        </div>
        <ClientOnly>
          <ItemAdd :item="item" />
        </ClientOnly>
      </div>
    </section>
  </article>
  <Alert v-if="items?.length === 0" type="info" class="mb-2">{{
    alert || $t("noDishes")
  }}</Alert>
</template>
