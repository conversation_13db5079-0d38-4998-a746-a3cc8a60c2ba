<template>
  <div class="w-full relative flex border-b pb-4 border-b-gray-200">
    <div class="w-36 h-24 flex justify-center items-center">
      <img
        class="rounded-2xl"
        :src="checkout.restaurant_logo"
        alt="Restaurant Image"
      />
    </div>
    <div class="flex flex-col w-full justify-between ml-4">
      <div>
        <div class="text-base">
          {{ formatDate(checkout.created_at) }}
        </div>
        <div class="text-lg font-semibold">
          {{ checkout.restaurant_name }}
        </div>
      </div>
      <div class="flex items-center w-full justify-between">
        <div>
          <div class="absolute right-0 top-0">
            {{ checkout.quantity }} {{ $t("dishes") }}
          </div>
          <div>{{ checkout.amount.toFixed(2) }} GEL</div>
        </div>
        <nuxt-link
          class="px-3 py-1.5 bg-amber-950 rounded-xl text-white text-sm font-medium"
          :to="checkout.order_details"
        >
          {{ $t("view") }}
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);
const { id } = useId();

const props = defineProps({
  checkout: {
    type: Object,
    required: true,
  },
});

const { checkout } = toRefs(props);

const formatDate = (dateStr: string) => {
  const date = dayjs(dateStr).utc(true).local().format("DD/MM/YYYY HH:mm");
  return date;
};
</script>
