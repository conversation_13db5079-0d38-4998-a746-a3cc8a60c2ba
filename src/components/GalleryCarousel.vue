<template>
  <div
    v-if="expandedImage.isExpanded"
    class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-10"
    @click="expandedImage.isExpanded = false"
  >
    <NuxtImg
      :src="expandedImage.url"
      class="max-w-full max-h-full rounded-lg transition-transform duration-500"
    />
  </div>
  <div class="relative w-full">
    <!-- Slider Wrapper -->
    <div class="overflow-hidden">
      <div
        class="flex transition-transform duration-300"
        :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
      >
        <!-- Slides -->
        <div
          v-for="(item, index) in items"
          :key="index"
          class="flex-shrink-0 w-full relative"
        >
          <!-- Image -->
          <NuxtImg
            :src="item.image ? item.image : config.public.defaultImage"
            class="w-full md:h-[50vh] sm:h-[35vh] h-[25vh] object-cover"
            :alt="`${takeTranslation(item).title}`"
            @click="expandImage(item.image)"
          />

          <div
            v-if="item.titlePosition === 'top'"
            class="absolute w-full sm:w-[70%] top-4 mb-8 left-1/2 backdrop-blur-xs bg-black/20 transform -translate-x-1/2 text-white px-4 p-2 rounded-2xl"
          >
            <h3 class="text-xl text-center md:text-3xl font-semibold">
              {{ takeTranslation(item).title }}
            </h3>
          </div>

          <!-- Dish Title for Other Positions -->
          <div
            v-else
            :class="`absolute w-full sm:w-[70%] md:w-[50%] bottom-0 mb-0 left-1/2 backdrop-blur-xs bg-black/20 transform -translate-x-1/2 text-white  px-4 p-2 rounded-2xl`"
          >
            <h3 class="text-xl text-center md:text-3xl font-semibold">
              {{ takeTranslation(item).title }}
            </h3>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Buttons -->
    <button
      class="absolute top-1/2 left-0 transform -translate-y-1/2 bg-black bg-opacity-30 text-white text-2xl font-bold p-3 rounded-full shadow-md"
      @click="prevSlide"
    >
      &lt;
    </button>
    <button
      class="absolute top-1/2 right-0 transform -translate-y-1/2 bg-black bg-opacity-30 text-white text-2xl font-bold p-3 rounded-full shadow-md"
      @click="nextSlide"
    >
      &gt;
    </button>

    <!-- Navigation Dots -->
    <div class="flex justify-center space-x-2 p-2">
      <span
        v-for="n in items.length"
        :key="n"
        @click="goToSlide(n - 1)"
        class="block w-2 h-2 bg-gray-400 rounded-full cursor-pointer"
        :class="{ 'bg-blue-500': n - 1 === currentSlide }"
      ></span>
    </div>
  </div>
</template>

<script setup>
const config = useRuntimeConfig();
const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  autoplayInterval: {
    type: Number,
    default: 3000, // default to 3 seconds
  },
});

const { takeTranslation } = useTranslation();
const currentSlide = ref(0);
const expandedImage = reactive({
  url: "",
  isExpanded: false,
});

const expandImage = (imageUrl) => {
  expandedImage.url = imageUrl;
  expandedImage.isExpanded = true;
};

let autoplayTimer = null;
const nextSlide = () => {
  if (currentSlide.value < props.items.length - 1) {
    currentSlide.value++;
  } else {
    currentSlide.value = 0;
  }
};

const prevSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--;
  } else {
    currentSlide.value = props.items.length - 1;
  }
};
const goToSlide = (index) => {
  currentSlide.value = index;
};

const startAutoplay = () => {
  autoplayTimer = setInterval(() => {
    nextSlide();
  }, props.autoplayInterval);
};

const stopAutoplay = () => {
  clearInterval(autoplayTimer);
};

// onMounted(startAutoplay);
// onUnmounted(stopAutoplay);
</script>

<style scoped>
.line-through {
  text-decoration: line-through;
}
</style>
