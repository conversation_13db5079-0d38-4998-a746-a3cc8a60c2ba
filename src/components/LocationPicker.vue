<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

const emit = defineEmits(['location-selected']);
const DEFAULT_LOCATION = {
  lat: 41.7151, // Replace with your default location
  lng: 44.8271
};

const position = ref({ lat: DEFAULT_LOCATION.lat, lng: DEFAULT_LOCATION.lng });
const map = ref<google.maps.Map | null>(null); // Add type hint
const marker = ref<google.maps.Marker | null>(null); // Add type hint
const locationError = ref('');
const isLocating = ref(false);
const retryCount = ref(0);
const MAX_RETRIES = 3;
const RETRY_DELAY = 2000; // 2 seconds between retries

// Refs to store listener handles
const mapClickListener = ref<google.maps.MapsEventListener | null>(null);
const markerDragListener = ref<google.maps.MapsEventListener | null>(null);

const initMap = async (centerPosition = position.value) => {
  // Clear previous listeners if any (safety measure)
  if (mapClickListener.value) google.maps.event.removeListener(mapClickListener.value);
  if (markerDragListener.value) google.maps.event.removeListener(markerDragListener.value);
  
  const mapElement = document.getElementById('map');
  if (!mapElement) {
    console.error("Map element not found");
    locationError.value = "Map container not found. Please refresh.";
    return;
  }

  const mapOptions = {
    center: { lat: centerPosition.lat, lng: centerPosition.lng },
    zoom: 16,
    mapTypeId: 'hybrid',
    gestureHandling: 'greedy',
    zoomControl: true,
    mapTypeControl: true,
    scaleControl: true,
    streetViewControl: false,
    rotateControl: true,
    fullscreenControl: true
  };
  
  try {
    // Ensure any previous map instance is cleared from the DOM perspective if needed
    // mapElement.innerHTML = ''; // Optional: if issues persist with replacing map instance
    
    map.value = new google.maps.Map(mapElement, mapOptions);
  
    // Create marker
    marker.value = new google.maps.Marker({
      position: centerPosition,
      map: map.value,
      draggable: true,
      animation: google.maps.Animation.DROP
    });

    // Add marker listeners and store handle
    markerDragListener.value = marker.value.addListener('dragend', () => {
      if (!marker.value) return;
      const pos = marker.value.getPosition();
      if (pos) {
        position.value = { lat: pos.lat(), lng: pos.lng() };
        emit('location-selected', position.value);
        locationError.value = '';
      }
    });

    // Add map click listener and store handle
    mapClickListener.value = map.value.addListener('click', (e: google.maps.MapMouseEvent) => {
      if (e.latLng && marker.value) {
        const pos = e.latLng;
        position.value = { lat: pos.lat(), lng: pos.lng() };
        marker.value.setPosition(pos);
        emit('location-selected', position.value);
        locationError.value = '';
      }
    });
  } catch (error) {
     console.error("Error initializing Google Map:", error);
     locationError.value = "Failed to load the map. Please try again.";
  }
};

const requestLocationPermission = async () => {
  try {
    // Check if permissions API is available
    if ('permissions' in navigator) {
      const permission = await navigator.permissions.query({ name: 'geolocation' });
      
      if (permission.state === 'denied') {
        // If denied, show instructions to enable location
        locationError.value = 'Please enable location access in your device settings to use this feature';
        // On iOS, direct users to settings
        if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
          if (confirm('Please enable location access in your iPhone settings. Would you like to open Settings now?')) {
            window.location.href = 'app-settings:';
          }
        }
        // On Android, direct users to settings
        else if (navigator.userAgent.match(/Android/i)) {
          if (confirm('Please enable location access in your device settings. Would you like to open Settings now?')) {
            window.location.href = 'intent://settings#Intent;scheme=package;end';
          }
        }
        return false;
      }
      return true;
    }
    return true;
  } catch (error) {
    console.error('Error checking permissions:', error);
    return true; // Proceed with regular getCurrentLocation flow
  }
};

const getCurrentLocation = async () => {
  isLocating.value = true;
  locationError.value = '';
  retryCount.value = 0;

  if (!navigator.geolocation) {
    locationError.value = 'Geolocation is not supported by your browser';
    isLocating.value = false;
    return false;
  }

  // Check permissions first
  const hasPermission = await requestLocationPermission();
  if (!hasPermission) {
    isLocating.value = false;
    return false;
  }

  const options = {
    enableHighAccuracy: true,
    timeout: 5000,
    maximumAge: 0
  };

  const attemptGetLocation = async (): Promise<GeolocationPosition> => {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, options);
    });
  };

  for (let i = 0; i <= MAX_RETRIES; i++) {
    try {
      const geoPosition = await attemptGetLocation();
      const newPosition = {
        lat: geoPosition.coords.latitude,
        lng: geoPosition.coords.longitude
      };

      position.value = newPosition;

      if (map.value && marker.value) {
        const latLng = new google.maps.LatLng(newPosition.lat, newPosition.lng);
        map.value.setCenter(latLng);
        marker.value.setPosition(latLng);
      }
      
      emit('location-selected', newPosition);
      locationError.value = '';
      isLocating.value = false;
      return true;

    } catch (error: any) {
      console.error('Geolocation error:', error);
      
      if (error.code === error.TIMEOUT && i < MAX_RETRIES) {
        retryCount.value = i + 1;
        locationError.value = `Retrying... (Attempt ${retryCount.value} of ${MAX_RETRIES})`;
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        continue;
      }

      let errorMessage = '';
      switch(error.code) {
        case error.PERMISSION_DENIED:
          return await requestLocationPermission(); // Try requesting permission again
        case error.POSITION_UNAVAILABLE:
          errorMessage = 'Unable to detect location. Select manually.';
          break;
        case error.TIMEOUT:
          errorMessage = 'Location detection timed out. Select manually.';
          break;
        default:
          errorMessage = 'Location detection failed. Select manually.';
      }
      locationError.value = errorMessage;
      isLocating.value = false;
      return false;
    }
  }
  isLocating.value = false;
  return false;
};

const handleManualLocation = () => {
  // No need to init map here. Just set error message.
  // Map should be initialized by onMounted. If user clicks this later, map should exist.
  if (!map.value) {
     // If map somehow didn't init, maybe try again or show error
     console.warn("Manual location selected but map is not initialized yet.")
     locationError.value = 'Map not ready. Please wait or refresh.';
     // Optionally, try initializing with default if needed:
     // initMap(DEFAULT_LOCATION); 
  } else {
     locationError.value = 'Please select your location by clicking or dragging the pin on the map';
     // Optionally recenter on default if needed
     // map.value.setCenter(DEFAULT_LOCATION);
     // marker.value?.setPosition(DEFAULT_LOCATION);
     // position.value = DEFAULT_LOCATION; 
     // emit('location-selected', position.value);
  }
};

onMounted(async () => {
  // Attempt to get current location first
  await getCurrentLocation(); 
  // Always initialize the map after attempting to get location.
  // It will use the updated position.value if successful, or DEFAULT_LOCATION otherwise.
  await initMap(position.value); 
});

onUnmounted(() => {
  // Explicitly remove listeners
  if (mapClickListener.value) {
    google.maps.event.removeListener(mapClickListener.value);
    mapClickListener.value = null;
  }
  if (markerDragListener.value) {
    google.maps.event.removeListener(markerDragListener.value);
    markerDragListener.value = null;
  }

  // It's generally not recommended to nullify the map instance itself in Google Maps API v3.
  // Letting it go out of scope is usually sufficient if the DOM element is removed.
  // However, nullifying the Vue refs is good practice.
  map.value = null;
  marker.value = null;

  // Optional: If you still face issues, consider removing the map div content
  // const mapElement = document.getElementById('map');
  // if (mapElement) mapElement.innerHTML = ''; 
});
</script>

<template>
  <div class="w-full space-y-4">
    <div id="map" class="w-full h-[400px] rounded-lg shadow-md"></div>
    
    <div v-if="locationError" 
         class="text-red-500 text-sm bg-red-50 p-4 rounded-lg border border-red-200">
      <p class="flex items-center">
        <Icon name="mdi:alert-circle" class="mr-2" />
        {{ locationError }}
      </p>
    </div>

    <div class="flex flex-col gap-2">
      <button 
        @click="getCurrentLocation"
        class="flex items-center justify-center w-full py-3 bg-velvet text-white rounded-lg hover:bg-velvet/90 transition-colors"
        :disabled="isLocating"
      >
        <Icon :name="isLocating ? 'mdi:loading' : 'mdi:location'" 
              class="mr-2" 
              :class="{ 'animate-spin': isLocating }" />
        {{ isLocating ? 'Detecting location...' : 'Get My Location' }}
      </button>
      
      <button 
        @click="handleManualLocation"
        class="flex items-center justify-center w-full py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
      >
        <Icon name="mdi:map-marker" class="mr-2" />
        Select Location Manually
      </button>
    </div>
  </div>
</template>

<style scoped>
#map {
  position: relative;
  border: 2px solid #e2e8f0;
}

/* Improve touch targets on mobile */
:deep(.gm-style) {
  font: inherit;
}

:deep(.gm-style-iw) {
  padding: 12px;
}
</style>
