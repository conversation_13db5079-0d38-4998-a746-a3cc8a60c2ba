<script setup lang="ts">
const config = useRuntimeConfig();

const props = defineProps({
  title: {
    type: String,
    default: "title",
  },
  image: {
    type: String,
    default: `/img/1.jpg`,
  },
});
</script>

<template>
  <article class="relative rounded-xl h-32 cursor-pointer">
    <NuxtImg
      :src="image ? image : config.public.defaultImage"
      class="object-cover border w-full rounded-xl h-full"
    />
    <div
      class="absolute left-0 top-0 right-0 bottom-0 bg-black opacity-20 rounded-xl"
    ></div>
    <div
      class="absolute font-medium tracking-wider bottom-0 h-1/3 w-full flex items-center justify-center text-white backdrop-blur-xs bg-black/20 rounded-b-xl"
    >
      {{ title }}
    </div>
  </article>
</template>
