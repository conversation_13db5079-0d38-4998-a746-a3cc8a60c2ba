<script setup>
import LanguageSwitcher from "./LanguageSwitcher";
import Button from "./Button";
const { openModal } = useModal();
const isAdmin = useState("isAdmin");
const router = useRouter();
const loading = useLoading();
const goToBack = () => {
  loading.value = true;
  router.back();
};
</script>
<template>
  <header class="p-2 bg-white">
    <article class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <button class="border border-velvet p-2 rounded-xl">
          <Icon
            name="ic:round-arrow-back-ios"
            size="25"
            class="text-velvet"
            @click="goToBack"
          />
        </button>
        <slot name="image"> </slot>
        <h1 class="font-semibold text-lg tracking-wider text-cocoa">
          <slot name="title"> </slot>
        </h1>
      </div>
      <div class="flex gap-3">
        <!-- <Button
          v-if="!isAdmin"
          @click="openModal('userStatus')"
          class="w-10 h-10 flex justify-center items-center bg-velvet"
        >
          <span class="text-3xl font-bold">G</span>
        </Button> -->

        <LanguageSwitcher />
      </div>
    </article>
    <slot name="aside"> </slot>
  </header>
</template>
