<script setup lang="ts">
import BaseModal from "./BaseModal.client.vue";
import Button from "./Button.vue";

interface Props {
  isShowModal: boolean;
  paymentUrl?: string;
  orderId?: string;
  amount?: number;
  isLoading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isShowModal: false,
  isLoading: false
});

const emit = defineEmits<{
  clickOut: [];
  close: [];
  openInNewTab: [];
}>();

const { t } = useI18n();

const openInNewTab = () => {
  if (props.paymentUrl) {
    window.open(props.paymentUrl, '_blank');
    emit('openInNewTab');
  }
};

const closeModal = () => {
  emit('close');
};
</script>

<template>
  <BaseModal :modalActive="isShowModal" @clickOut="$emit('clickOut')">
    <main class="px-6 py-4 flex flex-col gap-6 font-semibold text-cocoa justify-center items-center text-center max-w-md">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex flex-col items-center gap-4">
        <Icon name="svg-spinners:180-ring" class="text-4xl text-velvet" />
        <h3 class="text-xl font-semibold">{{ t('processingPayment') }}</h3>
        <p class="text-sm text-gray-600">{{ t('pleaseWait') }}</p>
      </div>

      <!-- Payment Ready State -->
      <div v-else-if="paymentUrl" class="flex flex-col items-center gap-4">
        <!-- Payment Icon -->
        <div class="w-16 h-16 bg-velvet rounded-full flex items-center justify-center">
          <Icon name="material-symbols:payment" class="text-2xl text-white" />
        </div>

        <!-- Title -->
        <h3 class="text-xl font-semibold">{{ t('paymentReady') }}</h3>
        
        <!-- Order Details -->
        <div v-if="orderId || amount" class="bg-gray-50 rounded-lg p-4 w-full">
          <p v-if="orderId" class="text-sm text-gray-600 mb-2">
            <span class="font-medium">{{ t('orderId') }}:</span> 
            <span class="font-mono text-xs">{{ orderId.substring(0, 8) }}...</span>
          </p>
          <p v-if="amount" class="text-lg font-bold text-velvet">
            {{ amount.toFixed(2) }} ₾
          </p>
        </div>

        <!-- Instructions -->
        <p class="text-sm text-gray-600 leading-relaxed">
          {{ t('paymentModalInstructions') }}
        </p>

        <!-- Embedded iframe for payment -->
        <div class="w-full bg-white border border-gray-200 rounded-lg overflow-hidden" style="height: 400px;">
          <iframe 
            :src="paymentUrl" 
            class="w-full h-full border-0"
            sandbox="allow-same-origin allow-scripts allow-forms allow-top-navigation"
            loading="lazy"
          ></iframe>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col gap-3 w-full">
          <!-- Open in New Tab Button -->
          <Button
            @click="openInNewTab"
            class="w-full py-3 bg-velvet hover:bg-velvet/90 transition-colors"
          >
            <div class="flex items-center justify-center gap-2">
              <Icon name="material-symbols:open-in-new" class="text-lg" />
              <span class="font-semibold">{{ t('openInNewTab') }}</span>
            </div>
          </Button>

          <!-- Close Button -->
          <Button
            @click="closeModal"
            class="w-full py-3 bg-white border border-velvet text-velvet hover:bg-gray-50 transition-colors"
          >
            <span class="font-semibold">{{ t('close') }}</span>
          </Button>
        </div>

        <!-- Help Text -->
        <p class="text-xs text-gray-500 mt-2">
          {{ t('paymentModalHelp') }}
        </p>
      </div>

      <!-- Error State -->
      <div v-else class="flex flex-col items-center gap-4">
        <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center">
          <Icon name="material-symbols:error" class="text-2xl text-white" />
        </div>
        <h3 class="text-xl font-semibold text-red-600">{{ t('paymentError') }}</h3>
        <p class="text-sm text-gray-600">{{ t('paymentErrorMessage') }}</p>
        <Button
          @click="closeModal"
          class="w-full py-3 bg-red-500 hover:bg-red-600 transition-colors"
        >
          <span class="font-semibold text-white">{{ t('close') }}</span>
        </Button>
      </div>
    </main>
  </BaseModal>
</template>

<style scoped>
/* Ensure iframe is responsive and secure */
iframe {
  border: none;
  width: 100%;
  height: 100%;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
