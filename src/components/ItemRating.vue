<template>
  <div class="flex items-center" v-if="!pending">
    <div class="flex" @mouseleave="resetRating">
      <template v-for="star in 5" :key="star">
        <Icon
          :name="getStarType(star)"
          class="cursor-pointer"
          :class="getClass(star)"
          @mouseover="hoverRating(star)"
          @click="rateDish(star)"
          size="24"
        />
      </template>
    </div>
    <span v-if="!userRating" class="ml-2 text-gray-600 hidden sm:inline"
      >({{ displayRating.toFixed(1) }} {{ $t("of") }}
      {{ item.ratings.length }})</span
    >
  </div>
  <div
    v-else
    class="w-8 border-none text-center flex items-center justify-center"
  >
    <Icon name="svg-spinners:180-ring" />
  </div>
</template>

<script setup>
const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
});

const { ratings, sendRating, pending } = useRatings();
const hoverRatingVal = ref(0);

const averageRating = computed(() => {
  if (props.item.ratings && props.item.ratings.length > 0) {
    const total = props.item.ratings.reduce(
      (acc, ratingObj) => acc + ratingObj.rate,
      0
    );
    return total / props.item.ratings.length;
  }
  return 0;
});

const userRating = computed(() => {
  const rating = ratings.value?.find((r) => r.dish_id === props.item.id);
  return rating ? rating.rate : null;
});

const displayRating = computed(() => {
  return userRating.value !== null ? userRating.value : averageRating.value;
});

const getStarType = (star) => {
  const fullStarThreshold = Math.floor(displayRating.value);
  const hasHalfStar = displayRating.value % 1 >= 0.5;

  if (star <= fullStarThreshold) {
    return "ic:sharp-star";
  } else if (star === fullStarThreshold + 1 && hasHalfStar) {
    return "ic:sharp-star-half";
  } else {
    return "ic:sharp-star-outline";
  }
};

const getClass = (star) => {
  if (star <= hoverRatingVal.value || userRating.value) {
    return "text-yellow-400 transition duration-150 ease-in-out";
  }
  return "text-gray-400 transition duration-150 ease-in-out";
};

const hoverRating = (star) => {
  if (!userRating.value) {
    hoverRatingVal.value = star;
  }
};

const resetRating = () => {
  hoverRatingVal.value = 0;
};

const rateDish = (star) => {
  if (!userRating.value) {
    sendRating(props.item.id, star);
  }
};
</script>

<style>
/* Optionally, add custom CSS here */
</style>
