<script setup lang="ts">
import { Order } from "types/orderTypes";
type PropTypes = {
  orders: Order[];
};

const props = defineProps<PropTypes>();
const { takeTranslation } = useTranslation();
const { restaurant } = useTable();

const getDate = (dateObject: Date) => {
  const year = dateObject.getFullYear();
  const month = dateObject.getMonth() + 1;
  const day = dateObject.getDate();
  const hour = dateObject.getHours();
  const minute = dateObject.getMinutes();
  const second = dateObject.getSeconds();

  const formattedTime = `${day}/${month}/${year} - ${hour}:${minute}`;

  return formattedTime;
};
</script>

<template>
  <article v-for="(order, index) in orders" :key="order.id" class="mt-5">
    <div class="border-b flex justify-between">
      <h2 class="text-base font-semibold text-cocoa pb-2">
        {{ $t("order") + " " + (index + 1) }}
      </h2>
      <time class="font-semibold">{{
        getDate(new Date(order.created_at as string))
      }}</time>
    </div>
    <ul class="pt-1">
      <li
        v-for="dish in order.dish"
        :key="dish.id"
        class="text-base text-cocoa font-medium flex justify-between mt-1"
      >
        <span>{{ takeTranslation(dish).title }}</span>
        <span
          >{{ dish.quantity }} -

          <span>
            {{
              dish.pivot && dish.pivot.promo && dish.pivot.promo.newPrice
                ? dish.pivot.promo.newPrice
                : dish.price
            }}{{ restaurant?.currency?.symbol }}
          </span>
          <span
            v-if="dish.pivot && dish.pivot.promo && dish.pivot.promo.newPrice"
            class="line-through mx-1"
          >
            ({{ dish.price }}{{ restaurant?.currency?.symbol }})
          </span></span
        >
      </li>
    </ul>
  </article>
</template>
