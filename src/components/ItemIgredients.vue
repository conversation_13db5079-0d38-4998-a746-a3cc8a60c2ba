<script setup lang="ts">
import { Ingredient } from "types/commonTypes";

interface Props {
  ingredients?: Ingredient[];
}

const props = defineProps<Props>();
const { takeTranslation } = useTranslation();
</script>

<template>
  <p v-if="ingredients" class="line-clamp-3">
    <span
      v-for="(ingredient, index) in ingredients"
      :key="ingredient.id || index"
      class="text-sm text-cocoa inline font-semibold"
    >
      {{ takeTranslation(ingredient).title
      }}<span v-if="index + 1 !== ingredients.length">, </span>
    </span>
  </p>
</template>

<style scoped></style>
