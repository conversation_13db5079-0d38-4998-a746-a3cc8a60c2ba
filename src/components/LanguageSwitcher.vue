<script setup lang="ts">
import { Dropdown, ListGroup, ListGroupItem } from "flowbite-vue";
const { locale, setLocale } = useLocale();
const config = useRuntimeConfig();

const languages = ["ka", "en", "ru", "el"];

const handleLanguageChange = (lang: string) => {
  setLocale(lang);
};
</script>

<template>
  <dropdown placement="left">
    <template #trigger>
      <Button class="w-10 h-10 flex justify-center items-center font-bold bg-velvet">
        {{ locale.toUpperCase() }}
      </Button>
    </template>
    <list-group>
      <template v-for="lang in languages" :key="lang">
        <list-group-item v-if="locale !== lang">
          <div
            @click="handleLanguageChange(lang)"
            class="flex w-full p-1 pl-0 gap-2 items-center"
          >
            <NuxtImg
              class="rounded-pill w-6"
              :src="`${config.public.r2Url}/${lang}.png`"
              alt=""
            />
            <span class="font-semibold">{{ $t(lang) }}</span>
          </div>
        </list-group-item>
      </template>
    </list-group>
  </dropdown>
</template>
