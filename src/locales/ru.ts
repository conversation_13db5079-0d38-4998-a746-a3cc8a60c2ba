export default {
  home: "дом",
  offers: "предложения",
  pages: "страницы",
  cart: "Тележка",
  new: "новое",
  goToTheRestaurant: "Сходи в ресторан",
  orderOnline: "заказ-онлайн",
  gallery: "галерея",
  restaurantInfo: "информация о ресторане",
  bookTable: "книжный столик",
  ratingReview: "повторный обзор",
  dishesSearchTitle: "Поиск Блюд",
  addToCart: "добавлять",
  viewMore: "Посмотреть больше",
  order: "заказ",
  subTotal: "Подытог",
  checkout: "проверка",
  yourOrder: "Ваш заказ",
  closed: "закрытый",
  open: "открывать",
  waiter: "Официант",
  total: "Весь",
  service: "Услуга",
  mins: "мин",
  tip: "совет",
  visitUs: "Приходите к нам снова!",
  thankYou: "Спасибо",
  yourCheck: "Ваш чек",
  table: "стол",
  space: "пространство",
  orderDate: "дата заказа",
  giveAdmin: "дайте админу",
  adminRequest: "запрос администратора",
  checkOrder: "проверьте заказ",
  choosePayMethod: "выберите способ оплаты",
  card: "карта",
  cash: "наличные",
  withTheSearchParameter: "с параметром Поиска",
  NoProductFound: "Товар не найден",
  chooseAmount: "Выберите сумму",
  noOrder: "никакого порядка",
  errorOccured: "произошла ошибка",
  youAreAdmin: "вы являетесь администратором",
  youAreNotAdmin: "вы не являетесь администратором",
  requestSentSuccessfully: "запрос отправлен успешно",
  AdminTransferred: "Статус администратора был успешно передан",
  AdminNotTransferred: "Статус администратора не удалось активировать",
  grantedAdminStatus: "Вам присвоен статус администратора",
  canNotCallwaiter: "не могу подозвать официанта",
  canNotRate: "не могу оценить",
  canNotUpdateCart: "Не удается обновить корзину",
  canNotAddDishInCart: "Извините, не могу добавить блюдо в корзину",
  NoOrdPayMethod: "Еще не выбран заказ или способ оплаты",
  canNotUpdateOrder: "не удается обновить заказ",
  canNotaddOrder: "не удается добавить заказ",
  canNotIncreaseOrder: "не удается увеличить заказ",
  calledWaiter: "подозвал официанта",
  ka: "Georgian",
  en: "English",
  ru: "Russian",
  el: "Greek",
  someoneAskForAdmin: "какой-то пользователь запрашивает имя администратора",
  cantDisplayDishes: "...ops есть (ОШИБКА) не удается отобразить блюда",
  noDishes: "Нет доступных блюд",
  thanksForOrder: "Благодарим за заказ",
  enter: "входить",
  basket: "корзина",
  main: "главный",
  shareCheck: "Поделиться чеком",
  orderCreatedSuccessfully: "Заказ успешно создан",
  menu: "Menu",
  chooseTip: "Выберите сумму чаевых",
  customerWantsToEdit: "The customer wants to be able to edit the order",
  accept: "Accept",
  deny: "Deny",
  requestReceived: "Request Received",
  receiptCheck: "Receipt Check",
  waitForBillCheck: "Wait for your bill to be checked at the desk",
  guest: "Guest",
  useMenuCantOrder: "You can use the menu, but you can't order",
  continueAsGuest: "Continue as a guest",
  serviceFee: "Обслуживание",
  previouslyOrdered: "Предыдущие заказы",
  howMuchCashUse: "Сколько наличных вы будете использовать?",
  notifyTheWaiter: "Мы сообщим официанту, чтобы он принес сдачу.",
  confirm: "Подтверждать",
  haveExactAmount: "у меня точно",
  payingWithCash: "Оплата наличными",
  payingWithCard: "Оплата картой",
  searchDish: "Search for the desired dish",
  noDish: "блюдо не найдено.",
  emptyCart: "Корзина пуста",
  restaurants: "Рестораны",
  howItWorks: "Как это работает",
  contactUs: "Связаться с нами",
  copyrightInformation: "Copyright Information",
  privacyPolicy: "политика конфиденциальности",
  termsOfUse: "Условия эксплуатации",
  discoverGreatRestaurants:
    "Откройте для себя отличные рестораны с <span class='font-extrabold'>MenuHub</span>",
  aboutUs: "О нас",
  whatsMenuHub: "Что такое MenuHub?",
  welcomeToMNU:
    "Добро пожаловать в MNU, идеальное место для знакомства с лучшими ресторанами города. С MNU вы можете открыть для себя мир кулинарных изысков, просматривать меню и выбирать блюда — и все это в одном приложении. Независимо от того, обедаете ли вы в ресторане или берете с собой, мы предоставим вам всю необходимую информацию.",
  browseRestaurants:
    "Просмотр ресторанов: изучите тщательно подобранный список ресторанов и их аппетитных блюд.",
  customizeYourOrder:
    "Настройте свой заказ: выберите любимые позиции из меню и настройте их по своему вкусу.",
  chooseYourSetting:
    "Выберите обстановку: выберите уютный ужин или возьмите заказ с собой.",
  exploreMore:
    "Узнайте больше: узнавайте расположение ресторанов, часы работы и будьте в курсе событий и новостей.",
  name: "ru_name",
  comments: "ru_comments",
  email: "ru_email",
  submit: "ru_submit",
  firstName: "First Name",
  lastName: "Last Name",
  restaurantName: "Restaurant Name",
  password: "Password",
  repeatPassword: "Repeat Password",
  iAgree: "I Agree to MenuHub’s <span class=''>Terms & Conditions</span>",
  nameRequired: "Name is required",
  restaurantNameRequired: "Restaurant name is required",
  invalidEmail: "Please provide correct email",
  invalidPhone: "Please provide correct phone",
  passwordRequired: "Password is required",
  passwordMismatch: "Make sure passwords match",
  agreeRequired: "Please agree to terms and conditions",
  validationFailed: "Validation Failed",
  registrationSuccess: "Registeration Successful",
  badRequest: "Bad Request",
  serverError: "Error occured. Please try again later",
  submissionFailed: "Submission Failed",
  register: "Register",
  totalPaid: "Итого",
  change: "Change",
  incorrectAmount: "Пожалуйста, введите правильную сумму!",
  canNotUpdateRatings: "Не могу обновить рейтинги",
  discount: "Скидка",
  apply: "Применять",
  of: "ru_Of",
  discountApplied: "Скидка применена!",
  invalidCode: "Неверный код",
  enterDiscountCode: "Пожалуйста, введите код скидки",
  haveDiscountCode: "Есть код скидки?",
  soon: "Soon",
  awaitingCheckout: "awaitingCheckout",
  profile: "Profile",
  myOrders: "My Orders",
  signUp: "Sign Up",
  account: "Account",
  changePassword: "Change Password",
  logout: "Logout",
  login: "Login",
  currentPassword: "Current Password",
  newPassword: "New Password",
  confirmNewPassword: "Confirm New Password",
  errorFirstNameRequired: "First Name is required",
  errorLastNameRequired: "Last Name is required",
  errorEmailRequired: "Email is required",
  errorInvalidEmail: "Error Invalid email",
  errorPhoneNumberRequired: "Phone Number is required",
  errorInvalidPhoneNumber: "Invalid phone number",
  pleaseEnterValidCredentials: "Please enter valid credentials",
  profileUpdatedSuccessfully: "Profile updated successfully",
  profileUpdateFailed: "Profile Update Failed",
  errorCurrentPasswordRequired: "Current Password is required",
  errorNewPasswordRequired: "New Password is required",
  errorNewPasswordSameAsCurrent:
    "New Password cannot be same as current password",
  errorConfirmNewPasswordRequired: "Confirm New password is required",
  errorNewPasswordMismatch:
    "New Password and Confirm New Password do not match",
  passwordChangedSuccessfully: "Password changed successfully",
  passwordChangeFailed: "Password Change Failed",
  placeAnOrder: "Place an order",
  hello: "Hello",
  orderHistory: "Order History",
  personalInformation: "Personal Information",
  other: "Other",
  view: "View",
  confirmPassword: "Confirm Password",
  thereAreNoOrders: "There are no orders",
  enterData: "Enter your data to enter the system",
  forgotPassword: "Forgot your password?",
  loginWithGoogle: "Login with Google",
  loginWithFacebook: "Login with Facebook",
  errorAgreeToTermsAndConditions: "Please agree to terms and conditions",
  agreeToTermsAndConditions:
    "I have read and agree to the <a class='text-velvet' href='https://menuhub.ge/terms-of-user'>Terms and Conditions</a> and the <a class='text-velvet' href='https://menuhub.ge/privacy'>Privacy Policy</a>",
  errorFirstNameLength: "First Name must be more than 2 characters",
  errorLastNameLength: "Last Name must be more than 2 characters",
  errorPasswordLength: "Password must be at least 8 characters",
  errorPasswordRequired: "Password is required",
  errorConfirmPasswordRequired: "Confirm Password is required",
  errorPasswordMismatch: "Passwords do not match",
  errorNewPasswordLength: "New Password must be at least 8 characters",
};
