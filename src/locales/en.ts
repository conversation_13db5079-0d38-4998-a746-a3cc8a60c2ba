export default {
  home: "Home",
  offers: "offers",
  pages: "pages",
  cart: "Cart",
  new: "new",
  goToTheRestaurant: "Go To the restaurant",
  orderOnline: "order-online",
  gallery: "gallery",
  restaurantInfo: "restaurant info",
  bookTable: "book table",
  ratingReview: "reting review",
  dishesSearchTitle: "Search For Dishes",
  addToCart: "ADD",
  viewMore: "View More",
  order: "Order",
  subTotal: "Sub Total",
  checkout: "CHECKOUT",
  yourOrder: "Your Order",
  closed: "CLOSED",
  open: "OPEN",
  waiter: "Waiter",
  total: "Total",
  service: "Service",
  mins: "mins",
  tip: "Tip",
  visitUs: "Visit us again!",
  thankYou: "Thank You",
  yourCheck: "Your Check",
  table: "table",
  space: "space",
  orderDate: "order date",
  giveAdmin: "give admin",
  adminRequest: "Admin request",
  checkOrder: "check order",
  choosePayMethod: "choose pay method",
  card: "card",
  cash: "cash",
  withTheSearchParameter: "with The Search Parameter",
  NoProductFound: "No product found",
  chooseAmount: "Choose an amount",
  noOrder: "No order",
  errorOccured: "an error occured",
  youAreAdmin: "you are admin",
  youAreNotAdmin: "you are not admin",
  requestSentSuccessfully: "request sent successfully",
  AdminTransferred: "Admin status was successfully transferred",
  AdminNotTransferred: "Admin status could not be activated",
  grantedAdminStatus: "You have been granted admin status",
  canNotCallwaiter: "can not call a waiter",
  canNotRate: "can not rate",
  canNotUpdateCart: "Can not update cart",
  canNotAddDishInCart: "Sorry can't add the dish in the cart",
  NoOrdPayMethod: "No order or payment method already selected",
  canNotUpdateOrder: "can not update order",
  canNotaddOrder: "can not add order",
  canNotIncreaseOrder: "can not increase order",
  calledWaiter: "called Waiter",
  ka: "GEORGIAN",
  en: "ENGLISH",
  ru: "RUSSIAN",
  el: "GREEK",
  someoneAskForAdmin: "some user ask for admin",
  cantDisplayDishes: "...ops there is (ERROR) can't display dishes",
  noDishes: "There are no dishes",
  thanksForOrder: "Thanks for order",
  enter: "enter",
  basket: "basket",
  main: "main",
  shareCheck: "Share Check",
  orderCreatedSuccessfully: "Order was created successfully",
  menu: "Menu",
  chooseTip: "Choose the amount of tip",
  customerWantsToEdit: "The customer wants to be able to edit the order",
  accept: "Accept",
  deny: "Deny",
  requestReceived: "Request Received",
  receiptCheck: "Receipt Check",
  waitForBillCheck: "Wait for your bill to be checked at the desk",
  guest: "Guest",
  useMenuCantOrder: "You can use the menu, but you can't order",
  continueAsGuest: "Continue as a guest",
  serviceFee: "Service Fee",
  previouslyOrdered: "Previous Orders",
  howMuchCashUse: "How much cash will you use?",
  notifyTheWaiter: "We will notify the waiter, to bring the changes",
  confirm: "Confirm",
  haveExactAmount: "I have exactly",
  payingWithCash: "Paying With Cash",
  payingWithCard: "Paying With Card",
  searchDish: "Search for the desired dish",
  noDish: "No dishes found",
  emptyCart: "Cart is empty",
  restaurants: "Restaurants",
  howItWorks: "How it Works",
  contactUs: "Contact Us",
  copyrightInformation: "Copyright Information",
  privacyPolicy: "Privacy Policy",
  termsOfUse: "Terms of Use",
  discoverGreatRestaurants:
    "Discover Great Restaurants with <span class='font-extrabold'>MenuHub</span>",
  aboutUs: "About Us",
  whatsMenuHub: "What's MenuHub?",
  welcomeToMNU:
    "Welcome to MNU, your go-to destination for exploring the finest restaurants in town. With MNU, you can discover a world of culinary delights, browse menus, and choose your dining experience—all in one app. Whether you're dining in or taking away, we've got you covered.",
  browseRestaurants:
    "Browse Restaurants: Explore a curated list of restaurants and their mouthwatering dishes.",
  customizeYourOrder:
    "Customize Your Order: Select your favorite items from the menu and customize them to your liking.",
  chooseYourSetting:
    "Choose Your Setting: Opt for a cozy dine-in experience or choose to take your order to-go.",
  exploreMore:
    "Explore More: Check out restaurant locations, operating hours, and stay updated with events and news.",
  name: "Name",
  comments: "Commetns",
  email: "Email",
  submit: "Submit",
  firstName: "First Name",
  lastName: "Last Name",
  restaurantName: "Restaurant Name",
  password: "Password",
  repeatPassword: "Repeat Password",
  iAgree:
    "I Agree to MenuHub’s <span class='text-[#f37832] underline font-bold'>Terms & Conditions</span>",
  nameRequired: "Name is required",
  restaurantNameRequired: "Restaurant name is required",
  invalidEmail: "Please provide correct email",
  invalidPhone: "Please provide correct phone",
  passwordRequired: "Password is required",
  passwordMismatch: "Make sure passwords match",
  agreeRequired: "Please agree to terms and conditions",
  validationFailed: "Validation Failed",
  registrationSuccess: "Registeration Successful",
  badRequest: "Bad Request",
  serverError: "Error occured. Please try again later",
  submissionFailed: "Submission Failed",
  register: "Register",
  totalPaid: "Total Paid",
  change: "Change",
  incorrectAmount: "Please enter correct amount!",
  canNotUpdateRatings: "Cannot update ratings",
  discount: "Discount",
  apply: "Apply",
  of: "Of",
  discountApplied: "Discount Applied!!",
  invalidCode: "Invalid Code",
  enterDiscountCode: "Please enter Discount Code",
  haveDiscountCode: "Have a Discount Code?",
  soon: "Soon",
  awaitingCheckout: "awaitingCheckout",
  profile: "Profile",
  myOrders: "My Orders",
  signUp: "Sign Up",
  account: "Account",
  changePassword: "Change Password",
  logout: "Logout",
  login: "Login",
  currentPassword: "Current Password",
  newPassword: "New Password",
  confirmNewPassword: "Confirm New Password",
  errorFirstNameRequired: "First Name is required",
  errorLastNameRequired: "Last Name is required",
  errorEmailRequired: "Email is required",
  errorInvalidEmail: "Error Invalid email",
  errorPhoneNumberRequired: "Phone Number is required",
  errorInvalidPhoneNumber: "Invalid phone number",
  pleaseEnterValidCredentials: "Please enter valid credentials",
  profileUpdatedSuccessfully: "Profile updated successfully",
  profileUpdateFailed: "Profile Update Failed",
  errorCurrentPasswordRequired: "Current Password is required",
  errorNewPasswordRequired: "New Password is required",
  errorNewPasswordSameAsCurrent:
    "New Password cannot be same as current password",
  errorConfirmNewPasswordRequired: "Confirm New password is required",
  errorNewPasswordMismatch:
    "New Password and Confirm New Password do not match",
  passwordChangedSuccessfully: "Password changed successfully",
  passwordChangeFailed: "Password Change Failed",
  placeAnOrder: "Place an order",
  hello: "Hello",
  orderHistory: "Order History",
  personalInformation: "Personal Information",
  other: "Other",
  view: "View",
  confirmPassword: "Confirm Password",
  thereAreNoOrders: "There are no orders",
  enterData: "Enter your data to enter the system",
  forgotPassword: "Forgot your password?",
  loginWithGoogle: "Login with Google",
  loginWithFacebook: "Login with Facebook",
  errorAgreeToTermsAndConditions: "Please agree to terms and conditions",
  agreeToTermsAndConditions:
    "I have read and agree to the <a class='text-velvet' href='https://menuhub.ge/terms'>Terms and Conditions</a> and the <a class='text-velvet' href='https://menuhub.ge/privacy'>Privacy Policy</a>",
  errorFirstNameLength: "First Name must be more than 2 characters",
  errorLastNameLength: "Last Name must be more than 2 characters",
  errorPasswordLength: "Password must be at least 8 characters",
  errorPasswordRequired: "Password is required",
  errorConfirmPasswordRequired: "Confirm Password is required",
  errorPasswordMismatch: "Passwords do not match",
  errorNewPasswordLength: "New Password must be at least 8 characters",
  // Payment Modal
  processingPayment: "Processing Payment",
  pleaseWait: "Please wait...",
  paymentReady: "Payment Ready",
  orderId: "Order ID",
  paymentModalInstructions: "Complete your payment using the form below or open it in a new tab for a better experience.",
  openInNewTab: "Open in New Tab",
  close: "Close",
  paymentModalHelp: "Having trouble? Try opening the payment page in a new tab.",
  paymentError: "Payment Error",
  paymentErrorMessage: "Unable to load payment page. Please try again.",
  deliveryLocation: "Delivery Location",
};
