#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Organization name
ORG="fesvi"
INCLUDE_PATTERN="mnu-"
EXCLUDE_PATTERN="mnu-api"

# Function to sync a repository
sync_repo() {
    local repo=$1
    echo -e "\n${GREEN}Found repository: ${YELLOW}${repo}${NC}"
    
    # Ask for confirmation
    echo -n "Do you want to sync this fork with its upstream? (y/n/q): "
    
    # Read a single character without requiring Enter
    while true; do
        read -n 1 -s confirm < /dev/tty
        case $confirm in
            [yY])
                echo "y"
                echo -e "${GREEN}Syncing ${repo}...${NC}"
                if gh repo sync "${repo}" 2>/dev/null; then
                    echo -e "${GREEN}✓ Successfully synced ${repo}${NC}"
                else
                    echo -e "${RED}✗ Failed to sync ${repo}${NC}"
                fi
                return
                ;;
            [nN])
                echo "n"
                echo -e "${YELLOW}Skipping ${repo}${NC}"
                return
                ;;
            [qQ])
                echo "q"
                echo -e "\n${YELLOW}Quitting...${NC}"
                exit 0
                ;;
        esac
    done
}

# Check if gh CLI is installed
if ! command -v gh &> /dev/null; then
    echo -e "${RED}Error: GitHub CLI (gh) is not installed.${NC}"
    echo "Please install it from: https://cli.github.com/"
    exit 1
fi

# Check if logged in to gh
if ! gh auth status &> /dev/null; then
    echo -e "${RED}Error: Not logged in to GitHub CLI.${NC}"
    echo "Please run: gh auth login"
    exit 1
fi

# Get all forked repositories in the organization containing "mnu-" but not "mnu-api"
echo -e "${GREEN}Fetching repositories containing '${YELLOW}${INCLUDE_PATTERN}${GREEN}' (excluding '${YELLOW}${EXCLUDE_PATTERN}${GREEN}') from ${YELLOW}${ORG}${GREEN} organization...${NC}"
repos=$(gh repo list $ORG --json name,isFork --jq ".[] | select(.isFork == true) | .name" | grep "^${INCLUDE_PATTERN}" | grep -v "${EXCLUDE_PATTERN}")

if [ -z "$repos" ]; then
    echo -e "${YELLOW}No matching repositories found in the ${ORG} organization${NC}"
    exit 0
fi

# Process each repository
while IFS= read -r repo; do
    sync_repo "${ORG}/${repo}"
done <<< "$repos"

echo -e "\n${GREEN}Fork synchronization process completed!${NC}"
