# MNU Development Guide

## Build & Development Commands
- `npm run dev` - Start dev server (HMR on port 24600)
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run generate` - Generate static site
- `npm run postinstall` - Prepare Nuxt (run after npm install)

## Code Style Guidelines
- **TypeScript**: Use strong typing with interfaces for objects
- **Components**: Use Vue 3 Composition API with `<script setup lang="ts">`
- **Naming**: 
  - Vue components: PascalCase.vue
  - Composables/utils: camelCase.ts
  - Layout files: kebab-case.vue
- **Formatting**: 
  - 2-space indentation
  - Single quotes
  - Semicolons at line endings
  - Trailing commas in multi-line objects/arrays
- **Component Structure**:
  - Template first, then script, then style
  - `<style scoped>` for component styles
  - Use Tailwind for styling
- **Error Handling**: Use toast notifications and createError for fatal errors
- **State Management**: Use composables for shared logic/state
- **i18n**: All user-facing strings should use i18n translation keys
- **Real-time**: Pusher integration for real-time updates

## Architecture Notes
- SPA mode (client-side only rendering)
- Supabase for backend/auth
- Mobile-first responsive design