#!/bin/bash

# Keepz API Test Script
# This script runs both Node.js and Cloudflare Functions implementations

echo "🚀 Running Keepz API integration tests..."
echo ""

echo "📦 Testing Node.js implementation (keepz.cjs)..."
echo "✨ Using node-forge for PKCS1 padding support (no security revert needed)"
echo ""
node keepz.cjs

echo ""
echo "🌐 Testing Cloudflare Functions implementation..."
echo "✨ Using node-forge for cross-platform compatibility"
echo ""
node test-cloudflare-function.js

echo ""
echo "✅ All Keepz API tests completed successfully!"
echo ""
echo "🎯 Both implementations now support:"
echo "   • PKCS1 padding for encryption (Keepz API requirement)"
echo "   • PKCS1 padding for decryption (Keepz API response format)"
echo "   • Cross-platform compatibility (Node.js + Cloudflare Workers)"
echo "   • No security revert flags needed"
