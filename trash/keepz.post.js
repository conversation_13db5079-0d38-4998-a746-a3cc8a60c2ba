// server/api/keepz.post.js
export default defineEventHandler(async (event) => {
  try {
    // Get runtime config for environment variables
    const config = useRuntimeConfig()
    
    // Environment variables (set in nuxt.config.ts or .env)
    const KEEPZ_INTEGRATOR_ID = config.keepzIntegratorId || "05382470-0ddb-4e36-8f23-4939f3b81ec9"
    const KEEPZ_IDENTIFIER = config.keepzIdentifier || config.keepzIntegratorId || "05382470-0ddb-4e36-8f23-4939f3b81ec9"
    const KEEPZ_RECEIVER_ID = config.keepzReceiverId || "0349ce8d-8061-4867-94a9-b9de0fb3fec6"
    const ORDER_AMOUNT = config.keepzOrderAmount || "25.50"
    const KEEPZ_BASE_URL = config.keepzBaseUrl || "https://gateway.dev.keepz.me/ecommerce-service"

    // RSA Keys from environment variables
    const KEEPZ_PUBLIC_KEY = config.keepzPublicKey
    const KEEPZ_PRIVATE_KEY = config.keepzPrivateKey

    // Validate required environment variables
    if (!KEEPZ_PUBLIC_KEY) {
      throw createError({
        statusCode: 400,
        statusMessage: 'KEEPZ_PUBLIC_KEY environment variable is required'
      })
    }

    if (!KEEPZ_PRIVATE_KEY) {
      throw createError({
        statusCode: 400,
        statusMessage: 'KEEPZ_PRIVATE_KEY environment variable is required'
      })
    }

    // Parse request body to get order details (optional override)
    let orderDetails = {}
    try {
      const body = await readBody(event)
      orderDetails = body || {}
    } catch (error) {
      // Use default values if no valid JSON body
    }

    // Generate UUID using crypto.randomUUID()
    function generateUUID() {
      return crypto.randomUUID()
    }

    // Convert base64 key to PEM format
    function convertToPemFormat(base64Key, keyType = 'PUBLIC') {
      const pemHeader = keyType === 'PUBLIC' ? '-----BEGIN PUBLIC KEY-----' : '-----BEGIN PRIVATE KEY-----'
      const pemFooter = keyType === 'PUBLIC' ? '-----END PUBLIC KEY-----' : '-----END PRIVATE KEY-----'
      const pemBody = base64Key.match(/.{1,64}/g).join('\n')
      return `${pemHeader}\n${pemBody}\n${pemFooter}`
    }

    // Encrypt data using RSA public key (Web Crypto API)
    async function encryptData(data, publicKeyPem) {
      try {
        // Import the public key
        const publicKey = await crypto.subtle.importKey(
          'spki',
          pemToArrayBuffer(publicKeyPem),
          {
            name: 'RSA-OAEP',
            hash: 'SHA-256'
          },
          false,
          ['encrypt']
        )

        // Encrypt the data
        const encoder = new TextEncoder()
        const dataBuffer = encoder.encode(data)
        const encrypted = await crypto.subtle.encrypt(
          {
            name: 'RSA-OAEP'
          },
          publicKey,
          dataBuffer
        )

        // Convert to base64
        return arrayBufferToBase64(encrypted)
      } catch (error) {
        console.error('Encryption failed:', error.message)
        throw error
      }
    }

    // Decrypt data using RSA private key (Web Crypto API)
    async function decryptData(encryptedData, privateKeyPem) {
      try {
        // Import the private key
        const privateKey = await crypto.subtle.importKey(
          'pkcs8',
          pemToArrayBuffer(privateKeyPem),
          {
            name: 'RSA-OAEP',
            hash: 'SHA-256'
          },
          false,
          ['decrypt']
        )

        // Convert base64 to ArrayBuffer
        const encryptedBuffer = base64ToArrayBuffer(encryptedData)

        // Decrypt the data
        const decrypted = await crypto.subtle.decrypt(
          {
            name: 'RSA-OAEP'
          },
          privateKey,
          encryptedBuffer
        )

        // Convert back to string
        const decoder = new TextDecoder()
        return decoder.decode(decrypted)
      } catch (error) {
        console.error('Decryption failed:', error.message)
        throw error
      }
    }

    // Helper function to convert PEM to ArrayBuffer
    function pemToArrayBuffer(pem) {
      const b64Lines = pem.replace(/-----[^-]+-----/g, '').replace(/\s/g, '')
      return base64ToArrayBuffer(b64Lines)
    }

    // Helper function to convert base64 to ArrayBuffer
    function base64ToArrayBuffer(base64) {
      const binaryString = atob(base64)
      const bytes = new Uint8Array(binaryString.length)
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }
      return bytes.buffer
    }

    // Helper function to convert ArrayBuffer to base64
    function arrayBufferToBase64(buffer) {
      const bytes = new Uint8Array(buffer)
      let binary = ''
      for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i])
      }
      return btoa(binary)
    }

    // Create the order data object
    const orderData = {
      amount: parseFloat(orderDetails.amount || ORDER_AMOUNT),
      receiverId: orderDetails.receiverId || KEEPZ_RECEIVER_ID,
      receiverType: orderDetails.receiverType || "BRANCH",
      integratorId: orderDetails.integratorId || KEEPZ_INTEGRATOR_ID,
      integratorOrderId: orderDetails.integratorOrderId || generateUUID()
    }

    console.log('Order data to be encrypted:', JSON.stringify(orderData, null, 2))

    // Convert JSON to string and encrypt it
    const jsonData = JSON.stringify(orderData)
    const publicKeyPem = convertToPemFormat(KEEPZ_PUBLIC_KEY, 'PUBLIC')

    console.log('Using public key for encryption')

    let encryptedData
    try {
      encryptedData = await encryptData(jsonData, publicKeyPem)
      console.log('Successfully encrypted data')
      console.log('Encrypted data length:', encryptedData.length)
    } catch (error) {
      console.error('Failed to encrypt data:', error.message)
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to encrypt data: ${error.message}`
      })
    }

    // Prepare the API request payload
    const requestPayload = {
      encryptedData: encryptedData,
      identifier: KEEPZ_IDENTIFIER
    }

    console.log('Request payload prepared')
    console.log('Identifier:', KEEPZ_IDENTIFIER)

    // Make the API request
    const apiUrl = `${KEEPZ_BASE_URL}/api/integrator/order`
    console.log('Making API call to:', apiUrl)

    const response = await $fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: requestPayload
    })

    console.log('API call successful!')

    if (response.encryptedData) {
      console.log('Encrypted response data received')

      // Decrypt the response using private key
      const privateKeyPem = convertToPemFormat(KEEPZ_PRIVATE_KEY, 'PRIVATE')

      try {
        const decryptedResponse = await decryptData(response.encryptedData, privateKeyPem)
        console.log('Successfully decrypted response')
        console.log('Decrypted response:', JSON.stringify(JSON.parse(decryptedResponse), null, 2))

        const decryptedObj = JSON.parse(decryptedResponse)
        
        const result = {
          success: true,
          systemId: decryptedObj.systemId,
          integratorOrderId: decryptedObj.integratorOrderId,
          urlForQR: decryptedObj.urlForQR,
          originalResponse: decryptedObj
        }

        if (decryptedObj.systemId) {
          console.log('Order created successfully!')
          console.log('System ID:', decryptedObj.systemId)
          console.log('Integrator Order ID:', decryptedObj.integratorOrderId)
          if (decryptedObj.urlForQR) {
            console.log('QR URL:', decryptedObj.urlForQR)
          }
        }

        return result
      } catch (decryptError) {
        console.error('Failed to decrypt response:', decryptError.message)
        throw createError({
          statusCode: 500,
          statusMessage: `Failed to decrypt response: ${decryptError.message}`
        })
      }
    } else {
      console.log('No encrypted data in response')
      return {
        success: true,
        message: 'No encrypted data in response',
        response: response
      }
    }

  } catch (error) {
    console.error('API error:', error.message)
    
    // Handle $fetch errors
    if (error.data) {
      throw createError({
        statusCode: error.status || 500,
        statusMessage: `API call failed: ${error.data}`
      })
    }
    
    // Re-throw createError instances
    if (error.statusCode) {
      throw error
    }
    
    // Handle other errors
    throw createError({
      statusCode: 500,
      statusMessage: `Server error: ${error.message}`
    })
  }
})
