#!/usr/bin/env node

const crypto = require('crypto');
const https = require('https');
const { URL } = require('url');

// Environment variables
const KEEPZ_INTEGRATOR_ID = "05382470-0ddb-4e36-8f23-4939f3b81ec9";
const KEEPZ_IDENTIFIER = "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAytWVQEPpjRiWDpDJtmYWEPHm+QPFhDSs2eWvkdNhUesE43bZ/b0SGnopAyONZOlLDmM89qLXIJtRUVxnkGiVxtvQ0bsPBwXM7eN3dVxJYVPDSyVeP4sP8hCG9HnjUdzDNae6ro12uaEAFFOCF8/xCd7DPKjTmAA6BA+FIi4cb7qo4cp2UXlQ+1j25Vu7l2ZIFwC7dv/XYgM6fgebTrlTR+9MdwZbtU30Tdi3Z6sbZ2L4OMt226w/n4nrNfCw/E03Iyp28y3ElTTBKGDLEZrqGodR+djFF+KHDMtRLjlqXZ0unDIOjFOZzALzidT++YmNYCHUTjySNtoW7r7KAZBPMgNUBL6K7HPsb2XX1a4nHMXrRi5JkoNfc47FZCp7juoOFe2HdQCnDwyD2Xc9svtJx7NfuoCBtbE9v0FhY7kFyTlpCeD6Mcnoge91n7IzxhY/IOQ4oX009RXWpWsXXfHA0gxQgmRYUJdQnG/3+jS4Ugi2GoA0k6IKEyjQA/yKPuiH7Hve5habYEhOtgvnwiSqvxwf1KzYvhSB+0MBNlUwMJVaWKbN5Tm+DlDTzAWlSLdw46WQWUu+POaToFSEWRG0XlEkKrVOqRM7o4yAJnB7fhG2RE24h4/467i2WeL6XCocyOAai3GtkB+TJGePNkx1YczDdoqejt614Qu0s0SubQcCAwEAAQ==";
const KEEPZ_RECEIVER_ID = "0349ce8d-8061-4867-94a9-b9de0fb3fec6";
const ORDER_AMOUNT = "25.50";
const KEEPZ_BASE_URL = process.env.KEEPZ_BASE_URL || "https://gateway.dev.keepz.me/ecommerce-service";

// Generate UUID
function generateUUID() {
    return crypto.randomUUID();
}

// Convert the base64 public key to PEM format
function convertToPemFormat(base64Key) {
    const pemHeader = '-----BEGIN PUBLIC KEY-----';
    const pemFooter = '-----END PUBLIC KEY-----';
    const pemBody = base64Key.match(/.{1,64}/g).join('\n');
    return `${pemHeader}\n${pemBody}\n${pemFooter}`;
}

// Encrypt data using RSA public key
function encryptData(data, publicKey) {
    try {
        const buffer = Buffer.from(data, 'utf8');
        const encrypted = crypto.publicEncrypt({
            key: publicKey,
            padding: crypto.constants.RSA_PKCS1_PADDING
        }, buffer);
        return encrypted.toString('base64');
    } catch (error) {
        console.error('Encryption failed:', error.message);
        throw error;
    }
}

// Create the order data object
const orderData = {
    amount: parseFloat(ORDER_AMOUNT), // Convert to number as per API docs
    receiverId: KEEPZ_RECEIVER_ID,
    receiverType: "BRANCH",
    integratorId: KEEPZ_INTEGRATOR_ID,
    integratorOrderId: generateUUID()
};

console.log('Order data to be encrypted:', JSON.stringify(orderData, null, 2));

// Convert JSON to string and encrypt it
const jsonData = JSON.stringify(orderData);
const publicKeyPem = convertToPemFormat(KEEPZ_IDENTIFIER);

console.log('Public key (PEM format):');
console.log(publicKeyPem);
console.log();

let encryptedData;
try {
    encryptedData = encryptData(jsonData, publicKeyPem);
    console.log('Successfully encrypted data');
} catch (error) {
    console.error('Failed to encrypt data:', error.message);
    process.exit(1);
}

// Prepare the API request payload
const requestPayload = {
    encryptedData: encryptedData,
    identifier: KEEPZ_INTEGRATOR_ID // Note: Using INTEGRATOR_ID as identifier, not the public key
};

console.log('Request payload structure:', {
    encryptedData: encryptedData.substring(0, 50) + '...',
    identifier: requestPayload.identifier
});
console.log();

// Parse the URL
const apiUrl = new URL(`${KEEPZ_BASE_URL}/api/integrator/order`);

// Configure the request options
const requestOptions = {
    hostname: apiUrl.hostname,
    port: apiUrl.port || (apiUrl.protocol === 'https:' ? 443 : 80),
    path: apiUrl.pathname,
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(JSON.stringify(requestPayload))
    }
};

console.log('Making request to:', `${apiUrl.protocol}//${apiUrl.hostname}${apiUrl.pathname}`);

// Make the HTTP request
const req = https.request(requestOptions, (res) => {
    let responseData = '';

    res.on('data', (chunk) => {
        responseData += chunk;
    });

    res.on('end', () => {
        console.log('Response Status:', res.statusCode);
        console.log('Response Headers:', JSON.stringify(res.headers, null, 2));
        console.log('Response Body:', responseData);
        
        if (res.statusCode === 200 && responseData) {
            try {
                const response = JSON.parse(responseData);
                console.log('\nAPI call successful!');
                console.log('Encrypted response data:', response.encryptedData);
                // Note: To decrypt the response, you would need the private key
            } catch (e) {
                console.log('Response is not valid JSON');
            }
        }
    });
});

req.on('error', (error) => {
    console.error('Request failed:', error.message);
    process.exit(1);
});

// Send the request
req.write(JSON.stringify(requestPayload));
req.end();