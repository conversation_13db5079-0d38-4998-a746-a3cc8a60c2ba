#!/usr/bin/env node

// Test script for the Cloudflare function locally
import { onRequestPost } from './functions/api/keepz.js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env file
function loadEnvFile() {
    const envPath = join(__dirname, '.env');
    try {
        const envContent = readFileSync(envPath, 'utf8');
        const lines = envContent.split('\n');
        const env = {};

        lines.forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#')) {
                const [key, ...valueParts] = line.split('=');
                if (key && valueParts.length > 0) {
                    let value = valueParts.join('=');
                    // Remove surrounding quotes if present
                    if ((value.startsWith('"') && value.endsWith('"')) ||
                        (value.startsWith("'") && value.endsWith("'"))) {
                        value = value.slice(1, -1);
                    }
                    env[key] = value;
                }
            }
        });
        return env;
    } catch (error) {
        console.error('Error loading .env file:', error.message);
        return {};
    }
}

async function testCloudflareFunction() {
    console.log('🧪 Testing Cloudflare function locally...\n');

    // Load environment variables
    const env = loadEnvFile();

    // Create mock request
    const mockRequest = {
        method: 'POST',
        json: async () => ({
            amount: 25.50,
            receiverId: env.KEEPZ_RECEIVER_ID,
            receiverType: "BRANCH",
            integratorId: env.KEEPZ_INTEGRATOR_ID
        })
    };

    // Create mock context
    const mockContext = {
        request: mockRequest,
        env: env
    };

    try {
        const response = await onRequestPost(mockContext);
        const responseText = await response.text();
        
        console.log('✅ Response Status:', response.status);
        console.log('📄 Response Body:');
        
        try {
            const responseJson = JSON.parse(responseText);
            console.log(JSON.stringify(responseJson, null, 2));
        } catch (e) {
            console.log(responseText);
        }
        
        if (response.status === 200) {
            console.log('\n🎉 Cloudflare function test successful!');
        } else {
            console.log('\n❌ Cloudflare function test failed');
        }
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error(error.stack);
    }
}

testCloudflareFunction();
