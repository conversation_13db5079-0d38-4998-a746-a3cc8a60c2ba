{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxtjs/google-fonts": "^3.0.1", "@nuxtjs/i18n": "8.0.0-beta.10", "@nuxtjs/supabase": "^1.1.5", "@nuxtjs/tailwindcss": "^6.8.0", "@types/uuid": "^9.0.2", "@types/vue-tel-input": "^2.1.6", "nuxt": "^3.6.1", "nuxt-icon": "^0.5.0"}, "dependencies": {"@nuxt/image": "^1.1.0", "@types/lodash": "^4.14.200", "@types/pusher-js": "^5.1.0", "@vueuse/core": "^10.2.1", "dayjs": "^1.11.10", "flowbite": "^1.8.1", "flowbite-vue": "^0.0.17-next.0", "lodash": "^4.17.21", "node-forge": "^1.3.1", "nuxt-swiper": "^1.1.0", "pusher-js": "^8.4.0-rc2", "uuid": "^9.0.0", "vue-slider-component": "^3.2.24", "vue-tel-input": "^8.1.4", "vue-toastification": "^2.0.0-rc.5"}}